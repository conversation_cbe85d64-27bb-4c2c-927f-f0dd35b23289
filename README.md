# HiddenGov - Government Contract Intelligence Platform

<div align="center">
  <strong>政府合同情报平台</strong><br />
  基于 Next.js 15 + Supabase 构建的政府采购数据分析平台
</div>

<br />

## 🎯 项目简介

HiddenGov 是一个专业的政府合同情报平台，帮助企业发现政府采购机会、分析竞争对手、寻找关键决策人。

### 核心功能
- 🔍 **智能搜索**：20+ 年历史合同数据检索
- 👥 **联系人发现**：采购官员和决策人信息
- 📊 **竞争分析**：供应商市场份额和趋势
- 📈 **数据洞察**：合同金额、周期、成功率分析
- 📤 **数据导出**：支持多种格式的数据导出

### 技术特性
- ✅ **Supabase 认证**：安全的用户认证和授权
- ✅ **邀请码系统**：受控的用户注册机制
- ✅ **7天免费试用**：新用户体验优化
- ✅ **响应式设计**：完美适配各种设备
- ✅ **TypeScript**：类型安全的开发体验

---

## 📋 基于开源模板

本项目基于优秀的开源模板 [next-shadcn-admin-dashboard](https://github.com/arhamkhnz/next-shadcn-admin-dashboard) 进行二次开发。

<div align="center">
  <strong>Next.js Admin Template built with TypeScript & Shadcn UI</strong><br />
  A modern admin dashboard template using Next.js 15, Tailwind CSS v4, App Router, TypeScript, and Shadcn UI.
</div>

<br />

<div align="center">
  <a href="https://next-shadcn-admin-dashboard.vercel.app">View Original Demo</a>
</div>

<br />

<p align="center">
  <a href="https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Farhamkhnz%2Fnext-shadcn-admin-dashboard">
    <img src="https://vercel.com/button" alt="Deploy with Vercel" />
  </a>
</p>

<br />

<div align="center">
  <img src="https://github.com/arhamkhnz/next-shadcn-admin-dashboard/blob/main/media/dashboard.png?version=5" alt="Dashboard Screenshot" width="75%">
</div>

## Project Vision

The goal is to create an open-source admin template that includes multiple example screens, prebuilt sections, and thoughtfully designed UI patterns, all supported by a clean architecture and proper project setup.

It aims to serve as a strong starting point for SaaS platforms, internal dashboards, and admin panels, with built-in support for multi-tenancy, RBAC, and feature-based scaling.

## Overview

This project uses `Next.js 15 (App Router)`, `TypeScript`, `Tailwind CSS v4`, and `Shadcn UI` as the main stack.
It also includes `Zod` for validation, `ESLint` and `Prettier` for linting and formatting, and `Husky` for pre-commit hooks.

This will support `React Hook Form`, `Zustand`, `TanStack Table`, and other related utilities, and will be added with upcoming screens. RBAC (Role-Based Access Control) with config-driven UI and multi-tenant UI support are also planned as part of the feature roadmap.

The current version uses the [Tweakcn Tangerine](https://tweakcn.com/) theme for UI.

> Looking for a **Next 14 + Tailwind CSS v3** version instead?
> Check out the [`archive/next14-tailwindv3`](https://github.com/arhamkhnz/next-shadcn-admin-dashboard/tree/archive/next14-tailwindv3) branch.
> This branch uses a different color theme and is not actively maintained, though I'm trying to keep it updated with the latest changes and screens.

## Screens

✅ Available
🚧 Coming Soon

### Dashboards
- ✅ Default
- 🚧 CRM
- 🚧 Analytics
- 🚧 eCommerce
- 🚧 Academy
- 🚧 Logistics

### Pages
- 🚧 Email
- 🚧 Chat
- 🚧 Calendar
- 🚧 Kanban
- 🚧 Invoice
- 🚧 Users
- 🚧 Roles
- 🚧 Authentication

## Colocation File System Architecture

The project follows a colocation-first file structure using the App Router. Feature-specific pages live alongside their components to maintain separation of concerns and reduce cross-import complexity.

```txt
src/
├── app/                      # Next.js App Router entry
│   ├── (external)/           # Public pages (e.g., marketing, feedback)
│
│   ├── (main)/               # Main application layout
│   │   ├── dashboard/
│   │   │   ├── layout.tsx    # Shared layout for dashboard routes
│   │   │   ├── default/      # Default overview dashboard
│   │   │   │   ├── components/
│   │   │   │   └── page.tsx
│   │   │   ├── ecommerce/
│   │   │   │   ├── components/
│   │   │   │   └── page.tsx
│   │   │   ├── email/
│   │   │   │   ├── components/
│   │   │   │   └── page.tsx
│   │   │   ├── users/
│   │   │   │   ├── components/
│   │   │   │   └── page.tsx
│   │   │   ├── profile/
│   │   │   │   ├── components/
│   │   │   │   └── page.tsx
│   ├── auth/                  # Auth section
│   │   ├── layout.tsx
│   │   ├── login/
│   │   │   ├── components/
│   │   │   └── page.tsx
│   │   ├── register/
│   │   │   ├── components/
│   │   │   └── page.tsx
│   │   ├── components/        # Shared auth components (e.g., buttons)
│
├── components/
│   ├── ui/                    # Reusable UI primitives (button, input, etc.)
│   ├── common/                # Shared layout/global components used across multiple areas
│
├── middleware.ts              # Middleware for handling auth/redirects
├── navigation/                # Navigation config for sidebar
├── hooks/                     # Custom React hooks
├── utils/                     # Utility/helper functions
├── server/                    # Server-only functions and server actions
├── config/                    # Project-wide configuration (e.g. theme, layout)
├── constants/                 # Static values like roles, app-level enums, routes, dummy data
```

If you want to dive deeper into this architecture pattern, check out [this repo](https://github.com/arhamkhnz/next-colocation-template).

## Getting Started

To set up and run this admin dashboard locally, follow these steps:

1. **Clone the repository**
   ```bash
   git clone https://github.com/arhamkhnz/next-shadcn-admin-dashboard.git
   ```

2. **Install dependencies**
   ```bash
    npm install
   ```
   > While installing, you may be prompted to use the `--force` or `--legacy-peer-deps` flag.
   > This is expected and safe — it’s due to a dependency from the Shadcn registry that references a breaking library version.

3. **Start the development server**
   ```bash
   npm run dev
   ```

Once running, the app will be available at [http://localhost:3000](http://localhost:3000)


---

> [!IMPORTANT]
> This project is frequently updated. If you’re working from a fork or previously cloned copy, check for the latest changes before syncing. Some updates may include breaking changes.

---

Feel free to open issues, feature requests, or start a discussion if you'd like to contribute or suggest improvements.

**Happy building!**

---

## 🚀 HiddenGov 快速开始指南

### 环境要求
- Node.js 18+
- npm 或 yarn
- Supabase 账户（用于认证和数据库）

### 安装步骤

1. **克隆 HiddenGov 仓库**
   ```bash
   git clone https://github.com/sandal5/hgov5.git
   cd hgov5
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **配置环境变量**
   ```bash
   cp .env.example .env.local
   ```

   编辑 `.env.local` 文件，填入你的 Supabase 配置：
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   ```

4. **设置数据库**

   在 Supabase 中执行 `docs/sql/` 目录下的 SQL 脚本来创建必要的表结构。

5. **启动开发服务器**
   ```bash
   npm run dev
   ```

应用将在 [http://localhost:3000](http://localhost:3000) 启动。

### 🔧 开发命令

```bash
# 开发模式
npm run dev

# 构建项目
npm run build

# 代码格式化
npm run format

# 类型检查
npm run type-check
```

## 📚 HiddenGov 项目文档

- [实施总结](docs/implementation-summary.md) - 项目实施详情
- [技术设计文档](docs/hiddengov-tdd-v2.md) - 技术架构设计
- [产品需求文档](docs/hiddengov_prd.md) - 产品功能需求
- [目录结构规范](docs/hiddengov-directory-v2.md) - 文件组织规范

## 🔐 认证功能

### 用户注册
- 需要有效的邀请码
- 支持邮箱验证
- 7天免费试用期

### 用户登录
- 邮箱密码登录
- "记住我" 功能
- 自动会话管理

### 权限控制
- 基于角色的访问控制 (RBAC)
- 路由级别保护
- 试用期自动检查

## 🎯 HiddenGov 核心功能

- 🔍 **智能搜索**：20+ 年历史合同数据检索
- 👥 **联系人发现**：采购官员和决策人信息
- 📊 **竞争分析**：供应商市场份额和趋势
- 📈 **数据洞察**：合同金额、周期、成功率分析
- 📤 **数据导出**：支持多种格式的数据导出

## 🛠️ 技术栈

- **前端**: Next.js 15, React 19, TypeScript, Tailwind CSS 4
- **UI 组件**: shadcn-ui
- **认证**: Supabase Auth
- **数据库**: Supabase (PostgreSQL)
- **表单**: React Hook Form + Zod
- **状态管理**: React Query

## 📞 技术支持

如需技术支持或有问题，请参考项目文档或联系开发团队。


