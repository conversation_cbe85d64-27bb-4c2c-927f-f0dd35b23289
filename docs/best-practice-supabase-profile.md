# Supabase 用户档案设置最佳实践

## 1. 方案背景与目标

- 统一管理用户档案（profile）相关的前端数据访问与更新逻辑。
- 保证数据安全、权限合规，便于团队协作和后续扩展。

## 2. Supabase 权限与安全最佳实践

- 前端仅使用 anon key，所有数据访问受 RLS（Row Level Security）和 Policy 控制。
- Service key 仅限后端服务使用，绝不暴露给前端。
- 密码修改通过 Supabase Auth API 实现，不直接操作 profile 表。

## 3. 统一封装 supabase client 步骤

- 在 `src/lib/supabaseClient.ts` 中统一初始化并导出 supabase client 实例。
- 读取环境变量 `NEXT_PUBLIC_SUPABASE_URL` 和 `NEXT_PUBLIC_SUPABASE_ANON_KEY`。

**示例：**

```ts
import { createClient } from "@supabase/supabase-js";
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error("Missing required Supabase environment variables");
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);
```

## 4. 封装 profile hooks 步骤

- 在 `src/hooks/useProfile.ts` 中封装获取、更新用户 profile 的逻辑。
- 只允许本人获取和更新自己的 profile，订阅信息和支付渠道只读。

**示例：**

```ts
import { useState, useEffect, useCallback } from "react";
import { supabase } from "../lib/supabaseClient";
export function useProfile() {
  /* ... */
}
```

## 5. 页面集成与 UI 设计建议

- 页面只负责 UI 展示和调用 hooks，不直接操作 supabase。
- 支持编辑 full name，订阅信息和支付渠道只读。
- 密码修改入口建议跳转到安全设置页，通过 Auth API 实现。

**示例结构：**

```tsx
import { useProfile } from "../../hooks/useProfile";
export default function SettingsPage() {
  /* ... */
}
```

## 6. 推荐目录结构

```
src/
  lib/
    supabaseClient.ts
  hooks/
    useProfile.ts
  app/
    settings/
      page.tsx
```

## 7. 典型代码片段

- supabase client 封装：见第3节
- profile hooks 封装：见第4节
- 页面集成：见第5节

## 8. 适用场景与扩展建议

- 适用于所有基于 Supabase 的用户中心、个人设置、SaaS 框架等场景。
- 后续如需多表聚合、复杂业务逻辑，可在 hooks 或后端 API 层扩展。
- 建议所有 supabase 相关操作均通过统一封装，便于维护和权限管理。

---

如需更多最佳实践或团队规范，建议持续完善本文档。
