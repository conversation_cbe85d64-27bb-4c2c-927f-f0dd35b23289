-- Hidden<PERSON>ov 生产环境数据库设置脚本
-- 在 Supabase SQL 编辑器中按顺序执行此脚本
-- 版本: 1.0.0
-- 创建日期: 2024-12-19

-- ============================================================================
-- 1. 创建用户档案表 (us_sam_u_profiles)
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.us_sam_u_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    auth_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
    email TEXT NOT NULL UNIQUE,
    full_name TEXT,
    avatar_url TEXT,
    role TEXT DEFAULT 'USER' CHECK (role IN ('USER', 'ADMIN', 'SUPER')),
    is_active BOOLEAN DEFAULT true,
    subscription_start TIMESTAMPTZ,
    subscription_end TIMESTAMPTZ,
    invited_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    invite_code_used TEXT,
    trial_start TIMESTAMPTZ DEFAULT NOW(),
    trial_end TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days'),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建更新时间戳触发器
CREATE OR REPLACE FUNCTION public.update_us_sam_u_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_us_sam_u_profiles_updated_at
    BEFORE UPDATE ON public.us_sam_u_profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.update_us_sam_u_profiles_updated_at();

-- ============================================================================
-- 2. 创建邀请码表 (us_sam_u_invite_codes)
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.us_sam_u_invite_codes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    code TEXT NOT NULL UNIQUE,
    is_used BOOLEAN NOT NULL DEFAULT false,
    used_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    max_uses INTEGER DEFAULT 1,
    current_uses INTEGER DEFAULT 0,
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    used_at TIMESTAMPTZ
);

-- 创建更新时间戳触发器
CREATE OR REPLACE FUNCTION public.update_us_sam_u_invite_codes_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_us_sam_u_invite_codes_updated_at
    BEFORE UPDATE ON public.us_sam_u_invite_codes
    FOR EACH ROW
    EXECUTE FUNCTION public.update_us_sam_u_invite_codes_updated_at();

-- ============================================================================
-- 3. 创建用户操作日志表 (us_sam_u_logs)
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.us_sam_u_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action TEXT NOT NULL,
    details JSONB DEFAULT '{}',
    ip_address TEXT,
    user_agent TEXT,
    session_id TEXT,
    referrer TEXT,
    page_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- 4. 创建索引以优化查询性能
-- ============================================================================

-- 用户档案表索引
CREATE INDEX IF NOT EXISTS idx_us_sam_u_profiles_auth_user_id ON public.us_sam_u_profiles(auth_user_id);
CREATE INDEX IF NOT EXISTS idx_us_sam_u_profiles_email ON public.us_sam_u_profiles(email);
CREATE INDEX IF NOT EXISTS idx_us_sam_u_profiles_role ON public.us_sam_u_profiles(role);
CREATE INDEX IF NOT EXISTS idx_us_sam_u_profiles_is_active ON public.us_sam_u_profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_us_sam_u_profiles_trial_end ON public.us_sam_u_profiles(trial_end);

-- 邀请码表索引
CREATE INDEX IF NOT EXISTS idx_us_sam_u_invite_codes_code ON public.us_sam_u_invite_codes(code);
CREATE INDEX IF NOT EXISTS idx_us_sam_u_invite_codes_is_used ON public.us_sam_u_invite_codes(is_used);
CREATE INDEX IF NOT EXISTS idx_us_sam_u_invite_codes_is_active ON public.us_sam_u_invite_codes(is_active);
CREATE INDEX IF NOT EXISTS idx_us_sam_u_invite_codes_expires_at ON public.us_sam_u_invite_codes(expires_at);

-- 日志表索引
CREATE INDEX IF NOT EXISTS idx_us_sam_u_logs_user_id ON public.us_sam_u_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_us_sam_u_logs_action ON public.us_sam_u_logs(action);
CREATE INDEX IF NOT EXISTS idx_us_sam_u_logs_created_at ON public.us_sam_u_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_us_sam_u_logs_session_id ON public.us_sam_u_logs(session_id);

-- ============================================================================
-- 5. 启用 Row Level Security (RLS)
-- ============================================================================

ALTER TABLE public.us_sam_u_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.us_sam_u_invite_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.us_sam_u_logs ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- 6. 创建 RLS 策略
-- ============================================================================

-- 用户档案表策略
DROP POLICY IF EXISTS "Users can view own profile" ON public.us_sam_u_profiles;
CREATE POLICY "Users can view own profile" ON public.us_sam_u_profiles
    FOR SELECT USING (auth.uid() = auth_user_id);

DROP POLICY IF EXISTS "Users can update own profile" ON public.us_sam_u_profiles;
CREATE POLICY "Users can update own profile" ON public.us_sam_u_profiles
    FOR UPDATE USING (auth.uid() = auth_user_id);

DROP POLICY IF EXISTS "Service role can manage all profiles" ON public.us_sam_u_profiles;
CREATE POLICY "Service role can manage all profiles" ON public.us_sam_u_profiles
    FOR ALL USING (auth.role() = 'service_role');

-- 邀请码表策略
DROP POLICY IF EXISTS "Public can read active invite codes for validation" ON public.us_sam_u_invite_codes;
CREATE POLICY "Public can read active invite codes for validation" ON public.us_sam_u_invite_codes
    FOR SELECT USING (is_active = true AND (expires_at IS NULL OR expires_at > NOW()));

DROP POLICY IF EXISTS "Service role can manage invite codes" ON public.us_sam_u_invite_codes;
CREATE POLICY "Service role can manage invite codes" ON public.us_sam_u_invite_codes
    FOR ALL USING (auth.role() = 'service_role');

DROP POLICY IF EXISTS "Admins can manage invite codes" ON public.us_sam_u_invite_codes;
CREATE POLICY "Admins can manage invite codes" ON public.us_sam_u_invite_codes
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.us_sam_u_profiles 
            WHERE auth_user_id = auth.uid() 
            AND role IN ('ADMIN', 'SUPER')
            AND is_active = true
        )
    );

-- 日志表策略
DROP POLICY IF EXISTS "Users can view own logs" ON public.us_sam_u_logs;
CREATE POLICY "Users can view own logs" ON public.us_sam_u_logs
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Service role can manage all logs" ON public.us_sam_u_logs;
CREATE POLICY "Service role can manage all logs" ON public.us_sam_u_logs
    FOR ALL USING (auth.role() = 'service_role');

DROP POLICY IF EXISTS "Admins can view all logs" ON public.us_sam_u_logs;
CREATE POLICY "Admins can view all logs" ON public.us_sam_u_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.us_sam_u_profiles 
            WHERE auth_user_id = auth.uid() 
            AND role IN ('ADMIN', 'SUPER')
            AND is_active = true
        )
    );

-- ============================================================================
-- 7. 创建初始测试数据
-- ============================================================================

-- 插入测试邀请码
INSERT INTO public.us_sam_u_invite_codes (code, created_by, max_uses, current_uses, expires_at, is_active)
VALUES 
    ('HGOV2024', NULL, 100, 0, NOW() + INTERVAL '90 days', true),
    ('TESTCODE', NULL, 10, 0, NOW() + INTERVAL '30 days', true),
    ('DEMO1234', NULL, 5, 0, NOW() + INTERVAL '7 days', true)
ON CONFLICT (code) DO NOTHING;

-- ============================================================================
-- 8. 创建有用的视图和函数
-- ============================================================================

-- 创建用户统计视图
CREATE OR REPLACE VIEW public.user_stats AS
SELECT 
    COUNT(*) as total_users,
    COUNT(*) FILTER (WHERE is_active = true) as active_users,
    COUNT(*) FILTER (WHERE role = 'ADMIN') as admin_users,
    COUNT(*) FILTER (WHERE trial_end > NOW()) as trial_users,
    COUNT(*) FILTER (WHERE subscription_end > NOW()) as subscribed_users
FROM public.us_sam_u_profiles;

-- 创建邀请码统计视图
CREATE OR REPLACE VIEW public.invite_code_stats AS
SELECT 
    COUNT(*) as total_codes,
    COUNT(*) FILTER (WHERE is_active = true) as active_codes,
    COUNT(*) FILTER (WHERE is_used = true) as used_codes,
    COUNT(*) FILTER (WHERE expires_at > NOW() OR expires_at IS NULL) as valid_codes
FROM public.us_sam_u_invite_codes;

-- 创建检查试用期的函数
CREATE OR REPLACE FUNCTION public.check_trial_status(user_uuid UUID)
RETURNS TABLE(
    has_valid_trial BOOLEAN,
    has_valid_subscription BOOLEAN,
    trial_days_left INTEGER,
    subscription_days_left INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (p.trial_end > NOW()) as has_valid_trial,
        (p.subscription_end > NOW()) as has_valid_subscription,
        GREATEST(0, EXTRACT(days FROM (p.trial_end - NOW()))::INTEGER) as trial_days_left,
        CASE 
            WHEN p.subscription_end IS NOT NULL 
            THEN GREATEST(0, EXTRACT(days FROM (p.subscription_end - NOW()))::INTEGER)
            ELSE 0
        END as subscription_days_left
    FROM public.us_sam_u_profiles p
    WHERE p.auth_user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 完成提示
SELECT 'HiddenGov production database setup completed successfully!' as status,
       NOW() as setup_time;
