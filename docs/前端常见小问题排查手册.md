# 前端常见小问题排查手册（React/Next.js）

---

## 1. 受控/非受控 input 报错

- **报错特征**：A component is changing an uncontrolled input to be controlled...
- **排查要点**：
  - input 的 value 是否有可能为 undefined/null？
  - 用 useState 管理的 input，初始值必须为字符串（如 ""），onChange 只传字符串。
  - 用表单库（如 react-hook-form）时，input 只用 {...register()}，不要加 value。
  - 表单库的 defaultValues 必须设置，所有字段初始值为字符串。
  - 不要混用受控/非受控写法。

---

## 2. 状态更新不生效/页面不刷新

- **排查要点**：
  - setState 是否用到了旧值？用 setX(prev => ...) 语法。
  - 是否直接修改了 state 对象/数组？要用新对象/新数组触发更新。
  - React 18+ 严格模式下，副作用会执行两次，注意副作用的幂等性。

---

## 3. useEffect 死循环/不触发

- **排查要点**：
  - 依赖数组是否写全？有无遗漏依赖？
  - 依赖项是否为对象/数组/函数？每次 render 都变，导致死循环。
  - 只想组件挂载时执行，依赖数组必须为 []。

---

## 4. 组件 props 传递异常

- **排查要点**：
  - 父组件是否传递了 undefined/null 给子组件的受控属性？
  - props 类型是否和子组件期望一致？
  - 组件封装时，props 兜底（如 value={props.value ?? ""}）。

---

## 5. 样式/主题切换不生效

- **排查要点**：
  - 是否有样式优先级冲突？（className 顺序、!important）
  - 主题切换是否依赖于 context/provider？provider 是否包裹了全局？
  - tailwind.config.js 是否配置了 darkMode？

---

## 6. 依赖冲突/包版本问题

- **排查要点**：
  - pnpm/yarn/npm 安装的依赖是否一致？有无多版本？
  - 删除 node_modules 和 lock 文件，重新 install。
  - 检查 package.json 依赖版本范围。

---

## 7. SSR/CSR 数据不一致

- **排查要点**：
  - 组件是否在服务端和客户端渲染结果不一致？（如 window/document 相关代码）
  - 用 useEffect 包裹只在客户端运行的逻辑。
  - Next.js 13+，use client/use server 指令是否正确。

---

## 8. 网络请求/接口报错

- **排查要点**：
  - fetch/axios 是否处理了异常（try/catch）？
  - 是否有跨域（CORS）问题？
  - 接口地址、参数、token 是否正确？

---

## 9. 依赖循环/无限递归

- **排查要点**：
  - import 路径是否有循环依赖？
  - 递归组件/函数是否有终止条件？

---

## 10. 其他高频小坑

- **key 警告**：列表渲染时，key 必须唯一且稳定。
- **ref 失效**：ref 只能绑定到 DOM 或 class 组件，不能直接绑定到函数组件。
- **环境变量**：Next.js 只暴露 NEXT*PUBLIC* 前缀的变量到前端。
- **构建失败**：检查 tsconfig、eslint、postcss 配置，是否有语法/类型错误。

---

# 实用建议

- 每次遇到报错，先看英文报错信息，80%都能定位到问题点。
- 小步快测，每次只改一处，立刻刷新页面。
- 多用 console.log 打印关键变量和状态。
- 遇到"玄学"问题，重启开发服务器、清空缓存、重装依赖。
- 团队内部建立"常见问题自查表"，新成员入职必读。
