# HiddenGov 数据库备份与监控策略

## 📋 概述

本文档描述了 HiddenGov 项目的数据库备份策略和监控方案，确保数据安全和系统稳定性。

## 🔄 备份策略

### 1. Supabase 自动备份

#### 配置自动备份
- **位置**: Supabase 控制台 > Settings > Database
- **备份频率**: 每日自动备份
- **保留期**: 7 天（免费计划）/ 30 天（Pro 计划）
- **备份时间**: 建议设置在用户活动最少的时间（如凌晨 2-4 点）

#### 备份验证
```sql
-- 检查最近的备份状态
SELECT 
    backup_id,
    created_at,
    status,
    size_bytes
FROM _supabase_backups 
ORDER BY created_at DESC 
LIMIT 5;
```

### 2. 手动备份脚本

#### 创建备份脚本
```bash
#!/bin/bash
# backup-database.sh

# 配置变量
PROJECT_ID="your-project-id"
DB_PASSWORD="your-database-password"
BACKUP_DIR="/path/to/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
pg_dump "******************************************************************/postgres" \
  --no-owner --no-privileges \
  --file="$BACKUP_DIR/hiddengov_backup_$DATE.sql"

# 压缩备份文件
gzip "$BACKUP_DIR/hiddengov_backup_$DATE.sql"

# 删除 7 天前的备份
find $BACKUP_DIR -name "hiddengov_backup_*.sql.gz" -mtime +7 -delete

echo "Backup completed: hiddengov_backup_$DATE.sql.gz"
```

#### 设置定时任务
```bash
# 编辑 crontab
crontab -e

# 添加每日凌晨 3 点备份任务
0 3 * * * /path/to/backup-database.sh >> /var/log/hiddengov-backup.log 2>&1
```

### 3. 关键数据导出

#### 用户数据导出
```sql
-- 导出用户档案数据
COPY (
    SELECT 
        id, auth_user_id, email, full_name, role, 
        is_active, created_at, trial_end, subscription_end
    FROM public.us_sam_u_profiles
    WHERE is_active = true
) TO '/tmp/user_profiles_backup.csv' WITH CSV HEADER;

-- 导出邀请码数据
COPY (
    SELECT 
        id, code, is_used, max_uses, current_uses, 
        expires_at, is_active, created_at
    FROM public.us_sam_u_invite_codes
    WHERE is_active = true
) TO '/tmp/invite_codes_backup.csv' WITH CSV HEADER;
```

## 📊 监控策略

### 1. 数据库性能监控

#### 关键指标监控
```sql
-- 创建监控视图
CREATE OR REPLACE VIEW public.system_health AS
SELECT 
    -- 用户统计
    (SELECT COUNT(*) FROM public.us_sam_u_profiles) as total_users,
    (SELECT COUNT(*) FROM public.us_sam_u_profiles WHERE is_active = true) as active_users,
    (SELECT COUNT(*) FROM public.us_sam_u_profiles WHERE trial_end > NOW()) as trial_users,
    
    -- 邀请码统计
    (SELECT COUNT(*) FROM public.us_sam_u_invite_codes WHERE is_active = true) as active_invite_codes,
    (SELECT COUNT(*) FROM public.us_sam_u_invite_codes WHERE is_used = true) as used_invite_codes,
    
    -- 今日活动统计
    (SELECT COUNT(*) FROM public.us_sam_u_logs WHERE created_at > CURRENT_DATE) as today_activities,
    (SELECT COUNT(DISTINCT user_id) FROM public.us_sam_u_logs WHERE created_at > CURRENT_DATE) as today_active_users,
    
    -- 系统状态
    NOW() as check_time;
```

#### 性能查询
```sql
-- 检查慢查询
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
WHERE mean_time > 1000  -- 超过 1 秒的查询
ORDER BY mean_time DESC 
LIMIT 10;

-- 检查表大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### 2. 应用层监控

#### 健康检查端点
创建 API 健康检查：

```typescript
// src/app/api/health/route.ts
import { NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase/admin';

export async function GET() {
  try {
    const supabase = createAdminClient();
    
    // 检查数据库连接
    const { data, error } = await supabase
      .from('us_sam_u_profiles')
      .select('count')
      .limit(1);
    
    if (error) {
      throw error;
    }
    
    // 检查系统健康状态
    const { data: healthData } = await supabase
      .from('system_health')
      .select('*')
      .single();
    
    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: 'connected',
      metrics: healthData
    });
    
  } catch (error) {
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    }, { status: 500 });
  }
}
```

### 3. 告警配置

#### Supabase 告警设置
- **位置**: Supabase 控制台 > Settings > Billing
- **配置项**:
  - 数据库使用量告警（80% 和 95%）
  - API 请求量告警
  - 存储空间告警
  - 带宽使用告警

#### 自定义告警脚本
```bash
#!/bin/bash
# monitor-system.sh

# 配置
WEBHOOK_URL="your-slack-webhook-url"
API_ENDPOINT="https://your-domain.com/api/health"

# 检查系统健康状态
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $API_ENDPOINT)

if [ $RESPONSE -ne 200 ]; then
    # 发送告警
    curl -X POST -H 'Content-type: application/json' \
        --data '{"text":"🚨 HiddenGov 系统健康检查失败！HTTP状态码: '$RESPONSE'"}' \
        $WEBHOOK_URL
fi
```

## 🔧 恢复策略

### 1. 数据恢复流程

#### 从 Supabase 备份恢复
1. 登录 Supabase 控制台
2. 进入 Settings > Database
3. 选择要恢复的备份点
4. 点击 "Restore" 按钮
5. 确认恢复操作

#### 从手动备份恢复
```bash
# 解压备份文件
gunzip hiddengov_backup_20241219_030000.sql.gz

# 恢复数据库
psql "******************************************************************/postgres" \
  -f hiddengov_backup_20241219_030000.sql
```

### 2. 灾难恢复计划

#### RTO/RPO 目标
- **RTO (Recovery Time Objective)**: 4 小时
- **RPO (Recovery Point Objective)**: 24 小时

#### 恢复步骤
1. **评估损失范围**
2. **通知相关人员**
3. **启动备用系统**（如有）
4. **从最近备份恢复数据**
5. **验证数据完整性**
6. **恢复服务**
7. **事后分析和改进**

## 📈 监控仪表盘

### 1. 关键指标

#### 业务指标
- 总用户数
- 活跃用户数
- 试用用户数
- 付费用户数
- 邀请码使用率

#### 技术指标
- 数据库连接数
- 查询响应时间
- 错误率
- 系统负载

### 2. 监控工具推荐

#### 免费工具
- **Supabase 内置监控**: 基础指标和告警
- **Google Analytics**: 用户行为分析
- **Uptime Robot**: 服务可用性监控

#### 付费工具
- **DataDog**: 全面的应用性能监控
- **New Relic**: 应用性能和基础设施监控
- **Sentry**: 错误追踪和性能监控

## ✅ 检查清单

### 日常检查
- [ ] 检查自动备份状态
- [ ] 查看系统健康指标
- [ ] 检查错误日志
- [ ] 验证监控告警

### 周度检查
- [ ] 测试备份恢复流程
- [ ] 检查存储使用情况
- [ ] 分析性能趋势
- [ ] 更新监控阈值

### 月度检查
- [ ] 完整的灾难恢复演练
- [ ] 备份策略评估
- [ ] 监控策略优化
- [ ] 安全审计

---

**通过实施这些备份和监控策略，HiddenGov 项目将具备高可用性和数据安全保障！** 🛡️
