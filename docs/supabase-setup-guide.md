# HiddenGov Supabase 生产环境配置指南

## 📋 概述

本指南将帮助您在 Supabase 中设置 HiddenGov 项目的生产环境数据库。

## 🚀 第一步：创建 Supabase 项目

### 1. 登录 Supabase 控制台
- 访问 [https://supabase.com](https://supabase.com)
- 使用您的账户登录或创建新账户

### 2. 创建新项目
- 点击 "New Project" 按钮
- 选择您的组织
- 填写项目信息：
  - **项目名称**: `hiddengov-production`
  - **数据库密码**: 生成强密码并保存
  - **地区**: 选择离您用户最近的地区
- 点击 "Create new project"

### 3. 等待项目初始化
- 项目创建通常需要 2-3 分钟
- 初始化完成后，您将看到项目仪表盘

## 🗄️ 第二步：执行数据库设置脚本

### 1. 打开 SQL 编辑器
- 在 Supabase 项目仪表盘中，点击左侧菜单的 "SQL Editor"
- 选择 "New query"

### 2. 执行生产环境设置脚本
- 复制 `docs/sql/production-setup.sql` 文件的全部内容
- 粘贴到 SQL 编辑器中
- 点击 "Run" 按钮执行脚本
- 确认看到成功消息：`HiddenGov production database setup completed successfully!`

### 3. 验证表结构
执行以下查询验证表是否正确创建：

```sql
-- 检查表是否存在
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE 'us_sam_u_%';

-- 检查 RLS 是否启用
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename LIKE 'us_sam_u_%';

-- 检查测试邀请码
SELECT code, is_active, expires_at 
FROM public.us_sam_u_invite_codes;
```

## 🔐 第三步：配置认证设置

### 1. 配置认证提供商
- 在项目仪表盘中，点击 "Authentication" > "Settings"
- 在 "Auth Providers" 部分：
  - 确保 "Email" 提供商已启用
  - 配置邮箱模板（可选）

### 2. 配置邮箱设置
- 在 "Auth" > "Settings" > "SMTP Settings" 中：
  - 配置您的 SMTP 服务器（推荐使用 SendGrid、Mailgun 等）
  - 或使用 Supabase 的默认邮箱服务

### 3. 配置 URL 设置
- 在 "Auth" > "Settings" > "URL Configuration" 中：
  - **Site URL**: `https://your-domain.com`（生产环境域名）
  - **Redirect URLs**: 添加以下 URL：
    - `https://your-domain.com/auth/callback`
    - `https://your-domain.com/auth/reset-password`
    - `http://localhost:3000/auth/callback`（开发环境）

## 🔑 第四步：获取 API 密钥

### 1. 获取项目 URL 和密钥
- 在项目仪表盘中，点击 "Settings" > "API"
- 复制以下信息：
  - **Project URL**: `https://your-project-id.supabase.co`
  - **anon public key**: `eyJ...`（公开密钥）
  - **service_role secret**: `eyJ...`（服务端密钥，保密）

### 2. 更新环境变量
在您的项目根目录中，更新 `.env.local` 文件：

```env
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# 应用配置
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_APP_NAME=HiddenGov

# 开发环境
NODE_ENV=production
```

## 🛡️ 第五步：安全配置

### 1. 配置 RLS 策略
- 我们的设置脚本已经自动配置了 RLS 策略
- 您可以在 "Authentication" > "Policies" 中查看和管理策略

### 2. 配置数据库备份
- 在 "Settings" > "Database" 中：
  - 启用自动备份
  - 设置备份保留期（推荐 7-30 天）

### 3. 配置监控和告警
- 在 "Settings" > "Billing" 中查看使用情况
- 设置使用量告警（可选）

## 📊 第六步：验证配置

### 1. 测试数据库连接
在您的本地项目中运行：

```bash
npm run dev
```

### 2. 测试认证功能
- 访问 `http://localhost:3000/auth/register`
- 使用测试邀请码 `HGOV2024` 注册新用户
- 验证邮箱验证流程
- 测试登录功能

### 3. 检查数据库记录
在 Supabase SQL 编辑器中执行：

```sql
-- 检查用户档案
SELECT * FROM public.us_sam_u_profiles;

-- 检查邀请码使用情况
SELECT * FROM public.us_sam_u_invite_codes WHERE is_used = true;

-- 检查用户操作日志
SELECT * FROM public.us_sam_u_logs ORDER BY created_at DESC LIMIT 10;
```

## 🔧 第七步：性能优化

### 1. 配置连接池
- Supabase 自动管理连接池
- 对于高流量应用，考虑升级到 Pro 计划

### 2. 监控查询性能
- 在 "Reports" 中查看数据库性能指标
- 优化慢查询（如有需要）

## 🚨 故障排除

### 常见问题

1. **连接失败**
   - 检查 API 密钥是否正确
   - 确认项目 URL 格式正确
   - 验证网络连接

2. **RLS 策略错误**
   - 检查用户是否有正确的权限
   - 验证策略配置是否正确

3. **邮箱验证问题**
   - 检查 SMTP 配置
   - 验证邮箱模板设置

### 获取帮助

- **Supabase 文档**: [https://supabase.com/docs](https://supabase.com/docs)
- **社区支持**: [https://github.com/supabase/supabase/discussions](https://github.com/supabase/supabase/discussions)
- **项目文档**: 查看 `docs/` 目录下的其他文档

## ✅ 配置完成检查清单

- [ ] Supabase 项目已创建
- [ ] 数据库设置脚本已执行
- [ ] 表结构验证通过
- [ ] 认证设置已配置
- [ ] API 密钥已获取并配置
- [ ] 环境变量已更新
- [ ] RLS 策略已启用
- [ ] 备份和监控已配置
- [ ] 本地测试通过
- [ ] 生产环境部署就绪

---

**配置完成后，您的 HiddenGov 项目将拥有一个安全、可扩展的生产级数据库！** 🎉
