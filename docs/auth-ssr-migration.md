# Next.js 15 + Supabase SSR认证重构迁移方案

---

## 一、重构目标与原则

1. **只用 `@supabase/ssr` 统一认证入口**，彻底移除 `@supabase/auth-helpers-*` 等旧方案。
2. **所有认证相关逻辑（登录、注册、登出、会话、profile、invite、logs）全部走 SSR 方案**，前后端一致。
3. **所有用户态、会话、权限判断、profile 读取、埋点等都只依赖 SSR 认证上下文**，不再混用本地存储、旧 hooks。
4. **所有页面、组件、API Route、hooks 只用 SSR 认证上下文获取用户信息**，不再用 useUser/useSession/useSupabaseClient 等旧 hooks。
5. **所有认证相关的 UI 组件、布局、侧边栏、API、hooks 全部重构为 SSR 认证驱动**。

---

## 二、全局架构设计

### 1. 认证上下文统一

- 全局用 SSR Supabase 客户端（`createServerClient`/`createBrowserClient`），在 `middleware.ts` 里自动刷新会话。
- 所有页面、API Route、组件、hooks 只通过 SSR 客户端获取用户和 profile。

### 2. 认证相关页面/组件

- 登录/注册/找回密码：全部用 SSR 客户端提交，前端只做表单校验，所有业务逻辑（邀请码、profile、logs）都在 API Route 里处理。
- 主布局/侧边栏/用户区：只通过 SSR 客户端获取用户和 profile，安全渲染。
- hooks：只保留 SSR 认证 hooks，移除所有旧的 useUser/useSession/useSupabaseClient。

### 3. API Route 认证

- 所有 API Route 统一用 SSR Supabase 客户端解析 cookie/session，获取当前用户。
- 后端业务逻辑（如 profile、invite、logs）全部在 API Route 里安全处理。

---

## 三、迁移与重构步骤

### 步骤1：全局 SSR Supabase 客户端配置

- `utils/supabase/client.ts`：客户端用 `createBrowserClient`
- `utils/supabase/server.ts`：服务端用 `createServerClient`
- `middleware.ts`：自动刷新会话，保护路由

### 步骤2：认证相关 API Route 重构

- `/api/auth/login` `/api/auth/register` `/api/auth/logout` `/api/auth/forgot-password` 等全部用 SSR 客户端实现
- 业务逻辑（邀请码校验、profile写入、logs埋点）全部在 API Route 内部处理

### 步骤3：认证相关页面/组件重构

- 登录、注册、找回密码页面全部只负责表单和 UI，提交到 API Route，前端不再直接操作 supabase.auth
- 登录成功后刷新 SSR session，跳转 dashboard
- 注册/激活/找回密码流程全部用 SSR session 驱动

### 步骤4：主布局、侧边栏、用户区重构

- 只通过 SSR 客户端获取用户和 profile，安全渲染
- 登出只调用 API Route，前端不再直接操作 supabase.auth

### 步骤5：hooks 重构

- 只保留 SSR 认证 hooks（如 useSSRUser/useSSRProfile），移除所有 useUser/useSession/useSupabaseClient

### 步骤6：API Route 认证统一

- 统一用 SSR 客户端解析 cookie/session，所有后端接口都能安全获取当前用户

---

## 四、全链路测试清单

### 1. 注册流程

- [x] 注册表单提交 `/api/auth/register`，校验邀请码、验证码，注册成功后跳转登录页。
- [x] 邮箱激活后可正常登录。

### 2. 登录/登出

- [x] 登录表单提交 `/api/auth/login`，校验验证码，登录成功后跳转 dashboard。
- [x] 登出请求 `/api/auth/logout`，会话清理，跳转首页。

### 3. 找回密码/重置密码

- [x] 找回密码表单提交 `/api/auth/forgot-password`，发送重置邮件。
- [x] 邮件链接跳转 `/reset-password?access_token=...`，表单提交 `/api/auth/reset-password`，密码重置成功后跳转登录页。

### 4. Profile 读取/更新

- [x] useProfile hook 通过 `/api/profile` 获取和 PATCH 更新用户信息。
- [x] 只允许本人操作，RLS 安全。

### 5. 受保护API与RLS

- [x] dashboard/stats、profile等API Route全部用SSR supabase客户端，未登录返回401。
- [x] 所有数据操作均受RLS保护，非本人无法访问/修改。

### 6. Token跳转与会话

- [x] 邮箱激活、重置密码等流程token通过URL传递，API Route消费token并安全处理。
- [x] 会话刷新、cookie同步全部由SSR客户端和middleware自动完成。

### 7. 兼容性与安全

- [x] 移除所有 `@supabase/auth-helpers-*` 相关依赖和Provider。
- [x] 前端不再直接操作 supabase.auth，全部通过API Route和SSR hooks。
- [x] 所有认证相关组件、API、hooks均已SSR化，安全、可维护。

---

## 五、最终迁移总结

- 本次重构彻底实现了 Next.js 15 + Supabase SSR 认证全链路安全、解耦、可维护。
- 前后端认证、会话、权限、profile、日志、邀请码等全部通过SSR API Route和hooks实现。
- 全局移除所有旧 hooks、Provider、客户端supabase操作，所有认证相关逻辑后端统一处理。
- 认证、注册、找回密码、重置密码、profile、受保护API、RLS等全链路测试通过。
- 后续如有新认证需求，全部参考本文档和现有SSR API实现。

> 本文档为最终迁移记录和开发参考，建议所有团队成员查阅并遵循。
