{"name": "studio-admin", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --fix"]}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@tabler/icons-react": "^3.33.0", "@tanstack/react-query": "^5.76.1", "@tanstack/react-table": "^8.21.3", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "eslint-plugin-unicorn": "^56.0.1", "initials": "^3.1.2", "input-otp": "^1.4.2", "lucide-react": "^0.453.0", "next": "^15.3.2", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-resizable-panels": "^3.0.2", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.16.0", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^22.15.18", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@typescript-eslint/parser": "^8.26.0", "eslint": "^9.27.0", "eslint-config-next": "^15.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-security": "^3.0.1", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.15.0", "husky": "^9.1.7", "lint-staged": "^15.5.2", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.5", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.0", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1"}}