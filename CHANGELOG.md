# Changelog

All notable changes to the HiddenGov project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Alpha-0.1.0] - 2024-12-19

### Added
- 🎉 **项目初始化**: 基于 next-shadcn-admin-dashboard 模板创建 HiddenGov 项目
- 🔐 **Supabase 集成**: 完整的认证和数据库集成
  - 客户端、服务端、管理端 Supabase 配置
  - TypeScript 类型定义
  - 环境变量模板
- 🔑 **认证功能**: 完整的用户认证系统
  - 用户登录（邮箱密码 + 记住我功能）
  - 用户注册（邀请码验证）
  - 密码强度验证
  - 表单验证（React Hook Form + Zod）
- 🛡️ **中间件保护**: 路由级别的访问控制
  - 认证状态检查
  - 试用期自动验证
  - 自动重定向逻辑
- 📊 **仪表盘功能**: 扩展原模板仪表盘
  - 用户信息显示
  - 试用期状态监控
  - 快速操作面板
  - 欢迎引导界面
- 🗄️ **数据库设计**: 核心表结构
  - `us_sam_u_profiles` - 用户档案表
  - `us_sam_u_invite_codes` - 邀请码管理表
  - `us_sam_u_logs` - 用户操作日志表
- 🔧 **API 路由**: RESTful API 接口
  - `/api/auth/register` - 用户注册
  - `/api/auth/validate-invite` - 邀请码验证
- 📁 **项目结构**: 规范化的文件组织
  - 遵循原模板的 colocation 架构
  - 新增 HiddenGov 特定目录结构
  - 保持与上游模板的兼容性

### Technical Details
- **前端技术栈**: Next.js 15.3.2, React 19.1.0, TypeScript, Tailwind CSS 4.1.5
- **UI 组件库**: shadcn-ui (基于 Radix UI)
- **认证服务**: Supabase Auth
- **数据库**: Supabase (PostgreSQL)
- **表单处理**: React Hook Form + Zod 验证
- **状态管理**: TanStack React Query
- **代码质量**: ESLint + Prettier + Husky

### Security Features
- ✅ 邀请码验证机制
- ✅ 密码强度要求（大小写字母 + 数字）
- ✅ 试用期自动检查（7天免费试用）
- ✅ 路由级别保护
- ✅ 用户操作日志记录
- ✅ Row Level Security (RLS) 准备

### Development Principles
- ✅ **保护原模板**: 最小化修改原有文件
- ✅ **增量开发**: 通过扩展而非替换实现功能
- ✅ **向后兼容**: 保持与原模板的兼容性
- ✅ **代码质量**: 通过 ESLint 和 Prettier 检查
- ✅ **类型安全**: 完整的 TypeScript 类型定义

### Build Status
- ✅ 项目构建成功
- ✅ 开发服务器运行正常
- ✅ 所有 ESLint 错误已修复
- ✅ TypeScript 类型检查通过
- ✅ 代码格式化完成

### Documentation
- 📖 [实施总结](docs/implementation-summary.md)
- 📖 [技术设计文档](docs/hiddengov-tdd-v2.md)
- 📖 [产品需求文档](docs/hiddengov_prd.md)
- 📖 [目录结构规范](docs/hiddengov-directory-v2.md)
- 📖 更新的 README.md 包含完整的安装和使用指南

### Known Issues
- ⚠️ Cloudflare Turnstile 集成待完成（当前使用占位符）
- ⚠️ 邮箱验证流程待实现
- ⚠️ 密码找回功能待开发

### Next Steps
- [ ] 配置 Supabase 实例
- [ ] 实现 Cloudflare Turnstile 验证
- [ ] 添加邮箱验证流程
- [ ] 开发密码找回功能
- [ ] 实现数据搜索功能
- [ ] 添加用户档案管理

---

## 版本说明

### Alpha 版本 (0.x.x)
- 核心功能开发阶段
- 基础架构搭建
- 认证系统实现
- 不建议生产环境使用

### Beta 版本 (1.0.0-beta.x)
- 功能完善阶段
- 性能优化
- 安全加固
- 测试环境部署

### 正式版本 (1.0.0+)
- 生产环境就绪
- 完整功能集
- 性能优化完成
- 安全审计通过

---

**当前版本**: Alpha-0.1.0  
**开发状态**: 🚧 开发中  
**生产就绪**: ❌ 否
