# Changelog

All notable changes to the HiddenGov project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Alpha-0.2.0] - 2024-12-19

### Added - 阶段一：Supabase 生产环境配置
- 🗄️ **生产环境数据库**: 创建统一的生产环境设置脚本
  - `docs/sql/production-setup.sql` - 完整的数据库初始化
  - 数据库索引优化和 RLS 策略配置
  - 初始测试数据和有用的视图函数
- 📚 **配置指南**: 详细的 Supabase 配置文档
  - `docs/supabase-setup-guide.md` - 完整的设置指南
  - `docs/database-backup-monitoring.md` - 备份和监控策略
- 🔧 **类型定义更新**: 匹配新数据库结构的 TypeScript 类型

### Added - 阶段二：完整认证功能开发
- 🔐 **密码管理**: 完整的密码找回和重置功能
  - `/auth/forgot-password` - 密码找回页面
  - `/auth/reset-password` - 密码重置页面
  - `/api/auth/forgot-password` 和 `/api/auth/reset-password` API
- 📧 **邮箱验证**: 完整的邮箱验证流程
  - `/auth/verify-email` - 邮箱验证确认页面
  - `/api/auth/verify-email` 和 `/api/auth/resend-verification` API
- ✨ **用户体验优化**: 改进的认证流程
  - 登录表单"记住我"功能和错误处理优化
  - `/auth/trial-expired` - 试用期过期页面
  - `/api/auth/log-activity` - 用户活动日志记录

### Added - 阶段三：Cloudflare Turnstile 全面集成
- 🛡️ **Turnstile 组件**: 完整的人机验证集成
  - `src/components/features/auth/turnstile-widget.tsx` - 可复用组件
  - `src/lib/turnstile.ts` - Turnstile 验证库
- 🔗 **前端集成**: 所有认证表单的 Turnstile 集成
  - 登录、注册、密码找回表单的安全验证
  - 开发模式支持和错误处理
- 🔧 **后端验证**: 真实的 Cloudflare API 集成
  - 所有认证 API 的 Turnstile token 验证
  - 安全的错误处理和用户反馈

### Enhanced
- 🎨 **用户体验**: 全面的交互优化
  - 加载状态、错误提示、验证反馈
  - 响应式设计和渐进增强
- 🔒 **安全加固**: 多层安全验证体系
  - 邀请码 + Turnstile + 邮箱验证
  - 数据库级别的 RLS 策略
  - 完整的用户操作审计日志

### Technical
- 🛠️ **代码质量**: 符合严格的代码标准
  - 降低函数复杂度，通过 ESLint 检查
  - 完整的 TypeScript 类型支持
  - 优化的导入顺序和代码格式
- 📦 **构建优化**: 成功的生产构建
  - 修复所有构建错误
  - 优化包大小和加载性能

### Documentation
- 📋 **完成报告**: 详细的项目状态文档
  - `docs/three-phase-completion-report.md` - 三阶段完成报告
  - 更新的项目状态和部署指南

## [Alpha-0.1.0] - 2024-12-19

### Added
- 🎉 **项目初始化**: 基于 next-shadcn-admin-dashboard 模板创建 HiddenGov 项目
- 🔐 **Supabase 集成**: 完整的认证和数据库集成
  - 客户端、服务端、管理端 Supabase 配置
  - TypeScript 类型定义
  - 环境变量模板
- 🔑 **认证功能**: 完整的用户认证系统
  - 用户登录（邮箱密码 + 记住我功能）
  - 用户注册（邀请码验证）
  - 密码强度验证
  - 表单验证（React Hook Form + Zod）
- 🛡️ **中间件保护**: 路由级别的访问控制
  - 认证状态检查
  - 试用期自动验证
  - 自动重定向逻辑
- 📊 **仪表盘功能**: 扩展原模板仪表盘
  - 用户信息显示
  - 试用期状态监控
  - 快速操作面板
  - 欢迎引导界面
- 🗄️ **数据库设计**: 核心表结构
  - `us_sam_u_profiles` - 用户档案表
  - `us_sam_u_invite_codes` - 邀请码管理表
  - `us_sam_u_logs` - 用户操作日志表
- 🔧 **API 路由**: RESTful API 接口
  - `/api/auth/register` - 用户注册
  - `/api/auth/validate-invite` - 邀请码验证
- 📁 **项目结构**: 规范化的文件组织
  - 遵循原模板的 colocation 架构
  - 新增 HiddenGov 特定目录结构
  - 保持与上游模板的兼容性

### Technical Details
- **前端技术栈**: Next.js 15.3.2, React 19.1.0, TypeScript, Tailwind CSS 4.1.5
- **UI 组件库**: shadcn-ui (基于 Radix UI)
- **认证服务**: Supabase Auth
- **数据库**: Supabase (PostgreSQL)
- **表单处理**: React Hook Form + Zod 验证
- **状态管理**: TanStack React Query
- **代码质量**: ESLint + Prettier + Husky

### Security Features
- ✅ 邀请码验证机制
- ✅ 密码强度要求（大小写字母 + 数字）
- ✅ 试用期自动检查（7天免费试用）
- ✅ 路由级别保护
- ✅ 用户操作日志记录
- ✅ Row Level Security (RLS) 准备

### Development Principles
- ✅ **保护原模板**: 最小化修改原有文件
- ✅ **增量开发**: 通过扩展而非替换实现功能
- ✅ **向后兼容**: 保持与原模板的兼容性
- ✅ **代码质量**: 通过 ESLint 和 Prettier 检查
- ✅ **类型安全**: 完整的 TypeScript 类型定义

### Build Status
- ✅ 项目构建成功
- ✅ 开发服务器运行正常
- ✅ 所有 ESLint 错误已修复
- ✅ TypeScript 类型检查通过
- ✅ 代码格式化完成

### Documentation
- 📖 [实施总结](docs/implementation-summary.md)
- 📖 [技术设计文档](docs/hiddengov-tdd-v2.md)
- 📖 [产品需求文档](docs/hiddengov_prd.md)
- 📖 [目录结构规范](docs/hiddengov-directory-v2.md)
- 📖 更新的 README.md 包含完整的安装和使用指南

### Known Issues
- ⚠️ Cloudflare Turnstile 集成待完成（当前使用占位符）
- ⚠️ 邮箱验证流程待实现
- ⚠️ 密码找回功能待开发

### Next Steps
- [ ] 配置 Supabase 实例
- [ ] 实现 Cloudflare Turnstile 验证
- [ ] 添加邮箱验证流程
- [ ] 开发密码找回功能
- [ ] 实现数据搜索功能
- [ ] 添加用户档案管理

---

## 版本说明

### Alpha 版本 (0.x.x)
- 核心功能开发阶段
- 基础架构搭建
- 认证系统实现
- 不建议生产环境使用

### Beta 版本 (1.0.0-beta.x)
- 功能完善阶段
- 性能优化
- 安全加固
- 测试环境部署

### 正式版本 (1.0.0+)
- 生产环境就绪
- 完整功能集
- 性能优化完成
- 安全审计通过

---

**当前版本**: Alpha-0.1.0
**开发状态**: 🚧 开发中
**生产就绪**: ❌ 否
