import { NextRequest, NextResponse } from "next/server";

import { createAdminClient } from "@/lib/supabase/admin";
import { createClient } from "@/lib/supabase/server";

interface ResendVerificationRequest {
  email: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: ResendVerificationRequest = await request.json();
    const { email } = body;

    if (!email) {
      return NextResponse.json({ message: "Email is required" }, { status: 400 });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({ message: "Please enter a valid email address" }, { status: 400 });
    }

    const adminSupabase = createAdminClient();

    // Check if user exists and is not already verified
    const { data: profile } = await adminSupabase
      .from("us_sam_u_profiles")
      .select("auth_user_id, email, is_active")
      .eq("email", email)
      .single();

    if (!profile) {
      // For security, we don't reveal if the email exists or not
      return NextResponse.json(
        { message: "If an account with that email exists and is unverified, we've sent a verification email." },
        { status: 200 },
      );
    }

    // Get the auth user to check verification status
    const { data: authUser } = await adminSupabase.auth.admin.getUserById(profile.auth_user_id);

    if (authUser.user?.email_confirmed_at) {
      return NextResponse.json({ message: "Email is already verified" }, { status: 400 });
    }

    const supabase = await createClient();

    // Resend verification email
    const { error } = await supabase.auth.resend({
      type: "signup",
      email: email,
      options: {
        emailRedirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/verify-email`,
      },
    });

    if (error) {
      console.error("Resend verification error:", error);
      return NextResponse.json({ message: "Failed to send verification email. Please try again." }, { status: 500 });
    }

    // Log the resend verification request
    await adminSupabase.from("us_sam_u_logs").insert({
      user_id: profile.auth_user_id,
      action: "VERIFICATION_EMAIL_RESENT",
      details: {
        email,
        ip_address: request.headers.get("x-forwarded-for") ?? "unknown",
        user_agent: request.headers.get("user-agent") ?? "unknown",
      },
    });

    return NextResponse.json(
      { message: "If an account with that email exists and is unverified, we've sent a verification email." },
      { status: 200 },
    );
  } catch (error) {
    console.error("Resend verification error:", error);
    return NextResponse.json({ message: "Internal server error" }, { status: 500 });
  }
}
