import { NextRequest, NextResponse } from "next/server";

import { createAdminClient } from "@/lib/supabase/admin";
import { createClient } from "@/lib/supabase/server";

interface LogActivityRequest {
  action: string;
  details?: Record<string, unknown>;
}

export async function POST(request: NextRequest) {
  try {
    const body: LogActivityRequest = await request.json();
    const { action, details = {} } = body;

    if (!action) {
      return NextResponse.json({ message: "Action is required" }, { status: 400 });
    }

    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const adminSupabase = createAdminClient();

    // Log the activity
    const { error } = await adminSupabase.from("us_sam_u_logs").insert({
      user_id: user.id,
      action,
      details: {
        ...details,
        ip_address: request.headers.get("x-forwarded-for") ?? "unknown",
        user_agent: request.headers.get("user-agent") ?? "unknown",
        referrer: request.headers.get("referer") ?? null,
      },
    });

    if (error) {
      console.error("Activity logging error:", error);
      return NextResponse.json({ message: "Failed to log activity" }, { status: 500 });
    }

    return NextResponse.json({ message: "Activity logged successfully" }, { status: 200 });
  } catch (error) {
    console.error("Log activity error:", error);
    return NextResponse.json({ message: "Internal server error" }, { status: 500 });
  }
}
