import { NextRequest, NextResponse } from "next/server";

import { createAdminClient } from "@/lib/supabase/admin";

interface VerifyEmailRequest {
  accessToken: string;
  refreshToken: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: VerifyEmailRequest = await request.json();
    const { accessToken, refreshToken } = body;

    if (!accessToken || !refreshToken) {
      return NextResponse.json({ message: "Missing required tokens" }, { status: 400 });
    }

    const adminSupabase = createAdminClient();

    // Set the session using the tokens
    const { data: sessionData, error: sessionError } = await adminSupabase.auth.setSession({
      access_token: accessToken,
      refresh_token: refreshToken,
    });

    if (sessionError || !sessionData.user) {
      return NextResponse.json({ message: "Invalid or expired verification link" }, { status: 400 });
    }

    const user = sessionData.user;

    // Check if email is already verified
    if (user.email_confirmed_at) {
      return NextResponse.json(
        {
          message: "<PERSON><PERSON> is already verified",
          alreadyVerified: true,
          email: user.email,
        },
        { status: 200 },
      );
    }

    // The email is automatically verified when the user clicks the verification link
    // and the session is established. We don't need to manually verify it.
    // Just ensure the user profile is marked as active.

    // Update user profile to mark as active (if not already)
    const { error: profileError } = await adminSupabase
      .from("us_sam_u_profiles")
      .update({ is_active: true })
      .eq("auth_user_id", user.id);

    if (profileError) {
      console.error("Profile update error:", profileError);
      // Don't fail the verification if profile update fails
    }

    // Log the email verification
    await adminSupabase.from("us_sam_u_logs").insert({
      user_id: user.id,
      action: "EMAIL_VERIFIED",
      details: {
        email: user.email,
        ip_address: request.headers.get("x-forwarded-for") ?? "unknown",
        user_agent: request.headers.get("user-agent") ?? "unknown",
      },
    });

    return NextResponse.json(
      {
        message: "Email verified successfully",
        email: user.email,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("Email verification error:", error);
    return NextResponse.json({ message: "Internal server error" }, { status: 500 });
  }
}
