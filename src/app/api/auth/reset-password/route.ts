import { NextRequest, NextResponse } from "next/server";

import { createAdminClient } from "@/lib/supabase/admin";

interface ResetPasswordRequest {
  password: string;
  accessToken: string;
  refreshToken: string;
}

// Helper function to validate password strength
function validatePassword(password: string): string | null {
  if (password.length < 6) {
    return "Password must be at least 6 characters long";
  }

  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/;
  if (!passwordRegex.test(password)) {
    return "Password must contain at least one uppercase letter, one lowercase letter, and one number";
  }

  return null;
}

export async function POST(request: NextRequest) {
  try {
    const body: ResetPasswordRequest = await request.json();
    const { password, accessToken, refreshToken } = body;

    if (!password || !accessToken || !refreshToken) {
      return NextResponse.json({ message: "Missing required fields" }, { status: 400 });
    }

    // Validate password strength
    const passwordError = validatePassword(password);
    if (passwordError) {
      return NextResponse.json({ message: passwordError }, { status: 400 });
    }

    const adminSupabase = createAdminClient();

    // Set the session using the tokens
    const { data: sessionData, error: sessionError } = await adminSupabase.auth.setSession({
      access_token: accessToken,
      refresh_token: refreshToken,
    });

    if (sessionError || !sessionData.user) {
      return NextResponse.json({ message: "Invalid or expired reset link" }, { status: 400 });
    }

    // Update the user's password
    const { error: updateError } = await adminSupabase.auth.updateUser({
      password: password,
    });

    if (updateError) {
      console.error("Password update error:", updateError);
      return NextResponse.json({ message: "Failed to update password. Please try again." }, { status: 500 });
    }

    // Log the password reset completion
    await adminSupabase.from("us_sam_u_logs").insert({
      user_id: sessionData.user.id,
      action: "PASSWORD_RESET_COMPLETED",
      details: {
        ip_address: request.headers.get("x-forwarded-for") ?? "unknown",
        user_agent: request.headers.get("user-agent") ?? "unknown",
      },
    });

    // Sign out the user to force them to log in with the new password
    await adminSupabase.auth.signOut();

    return NextResponse.json({ message: "Password updated successfully" }, { status: 200 });
  } catch (error) {
    console.error("Reset password error:", error);
    return NextResponse.json({ message: "Internal server error" }, { status: 500 });
  }
}
