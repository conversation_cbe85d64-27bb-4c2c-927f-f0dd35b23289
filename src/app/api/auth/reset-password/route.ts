import { NextRequest, NextResponse } from "next/server";

import { createAdminClient } from "@/lib/supabase/admin";

interface ResetPasswordRequest {
  password: string;
  accessToken: string;
  refreshToken: string;
}

// Helper function to validate password strength
function validatePassword(password: string): string | null {
  if (password.length < 6) {
    return "Password must be at least 6 characters long";
  }

  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/;
  if (!passwordRegex.test(password)) {
    return "Password must contain at least one uppercase letter, one lowercase letter, and one number";
  }

  return null;
}

// Helper function to update password
// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function updateUserPassword(adminSupabase: any, password: string) {
  const { error } = await adminSupabase.auth.updateUser({ password });
  if (error) {
    console.error("Password update error:", error);
    throw new Error("Failed to update password. Please try again.");
  }
}

// Helper function to log password reset
// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function logPasswordReset(adminSupabase: any, userId: string, request: NextRequest) {
  await adminSupabase.from("us_sam_u_logs").insert({
    user_id: userId,
    action: "PASSWORD_RESET_COMPLETED",
    details: {
      ip_address: request.headers.get("x-forwarded-for") ?? "unknown",
      user_agent: request.headers.get("user-agent") ?? "unknown",
    },
  });
}

export async function POST(request: NextRequest) {
  try {
    const body: ResetPasswordRequest = await request.json();
    const { password, accessToken, refreshToken } = body;

    if (!password || !accessToken || !refreshToken) {
      return NextResponse.json({ message: "Missing required fields" }, { status: 400 });
    }

    const passwordError = validatePassword(password);
    if (passwordError) {
      return NextResponse.json({ message: passwordError }, { status: 400 });
    }

    const adminSupabase = createAdminClient();
    const { data: sessionData, error: sessionError } = await adminSupabase.auth.setSession({
      access_token: accessToken,
      refresh_token: refreshToken,
    });

    if (sessionError || !sessionData.user) {
      return NextResponse.json({ message: "Invalid or expired reset link" }, { status: 400 });
    }

    await updateUserPassword(adminSupabase, password);
    await logPasswordReset(adminSupabase, sessionData.user.id, request);
    await adminSupabase.auth.signOut();

    return NextResponse.json({ message: "Password updated successfully" }, { status: 200 });
  } catch (error) {
    console.error("Reset password error:", error);
    const message = error instanceof Error ? error.message : "Internal server error";
    return NextResponse.json({ message }, { status: 500 });
  }
}
