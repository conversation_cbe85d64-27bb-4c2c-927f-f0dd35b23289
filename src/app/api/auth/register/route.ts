import { NextRequest, NextResponse } from "next/server";

import { createAdminClient } from "@/lib/supabase/admin";
import { createClient } from "@/lib/supabase/server";
import { verifyTurnstileToken, isValidTurnstileToken } from "@/lib/turnstile";

interface RegisterRequest {
  email: string;
  password: string;
  fullName: string;
  inviteCode: string;
  turnstileToken: string;
}

async function validateInviteCode(inviteCode: string) {
  const adminSupabase = createAdminClient();
  const { data: inviteData, error: inviteError } = await adminSupabase
    .from("us_sam_u_invite_codes")
    .select("*")
    .eq("code", inviteCode)
    .eq("is_used", false)
    .single();

  if (inviteError || !inviteData) {
    throw new Error("Invalid or expired invite code");
  }

  if (inviteData.expires_at && new Date(inviteData.expires_at) < new Date()) {
    throw new Error("Invite code has expired");
  }

  return inviteData;
}

async function createUserAccount(email: string, password: string, fullName: string) {
  const supabase = await createClient();
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: fullName,
      },
    },
  });

  if (authError) {
    throw new Error(authError.message);
  }

  if (!authData.user) {
    throw new Error("Failed to create user account");
  }

  return authData.user;
}

// Helper function to validate registration input
function validateRegistrationInput(body: RegisterRequest): string | null {
  const { email, password, fullName, inviteCode, turnstileToken } = body;

  if (!email || !password || !fullName || !inviteCode || !turnstileToken) {
    return "All fields are required";
  }

  if (!isValidTurnstileToken(turnstileToken)) {
    return "Invalid verification token";
  }

  return null;
}

// Helper function to create user profile
// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function createUserProfile(adminSupabase: any, user: any, email: string, fullName: string) {
  const { error: profileError } = await adminSupabase.from("us_sam_u_profiles").insert({
    auth_user_id: user.id,
    email,
    full_name: fullName,
    role: "USER",
    is_active: true,
  });

  if (profileError) {
    // If profile creation fails, we should clean up the auth user
    await adminSupabase.auth.admin.deleteUser(user.id);
    throw new Error("Failed to create user profile");
  }
}

// Helper function to finalize registration
// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function finalizeRegistration(
  adminSupabase: any,
  user: any,
  inviteData: any,
  email: string,
  inviteCode: string,
  request: NextRequest,
) {
  // Mark invite code as used
  await adminSupabase
    .from("us_sam_u_invite_codes")
    .update({
      is_used: true,
      used_by: user.id,
      used_at: new Date().toISOString(),
    })
    .eq("id", inviteData.id);

  // Log registration
  await adminSupabase.from("us_sam_u_logs").insert({
    user_id: user.id,
    action: "USER_REGISTERED",
    details: {
      email,
      invite_code: inviteCode,
      ip_address: request.headers.get("x-forwarded-for") ?? "unknown",
      user_agent: request.headers.get("user-agent") ?? "unknown",
    },
  });
}

export async function POST(request: NextRequest) {
  try {
    const body: RegisterRequest = await request.json();
    const { email, password, fullName, inviteCode, turnstileToken } = body;

    // Validate input
    const validationError = validateRegistrationInput(body);
    if (validationError) {
      return NextResponse.json({ message: validationError }, { status: 400 });
    }

    // Verify Turnstile token with Cloudflare
    const isValidToken = await verifyTurnstileToken(
      turnstileToken,
      request.headers.get("x-forwarded-for") ?? undefined,
    );

    if (!isValidToken) {
      return NextResponse.json({ message: "Verification failed. Please try again." }, { status: 400 });
    }

    // Validate invite code
    const inviteData = await validateInviteCode(inviteCode);

    // Create user account
    const user = await createUserAccount(email, password, fullName);

    // Create user profile
    const adminSupabase = createAdminClient();
    await createUserProfile(adminSupabase, user, email, fullName);

    // Finalize registration
    await finalizeRegistration(adminSupabase, user, inviteData, email, inviteCode, request);

    return NextResponse.json({ message: "Registration successful" }, { status: 201 });
  } catch (error) {
    console.error("Registration error:", error);
    const message = error instanceof Error ? error.message : "Internal server error";
    return NextResponse.json({ message }, { status: 500 });
  }
}
