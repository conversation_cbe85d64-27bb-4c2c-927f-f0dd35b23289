import { NextRequest, NextResponse } from "next/server";

import { createAdminClient } from "@/lib/supabase/admin";
import { createClient } from "@/lib/supabase/server";
import { verifyTurnstileToken, isValidTurnstileToken } from "@/lib/turnstile";

interface RegisterRequest {
  email: string;
  password: string;
  fullName: string;
  inviteCode: string;
  turnstileToken: string;
}

async function validateInviteCode(inviteCode: string) {
  const adminSupabase = createAdminClient();
  const { data: inviteData, error: inviteError } = await adminSupabase
    .from("us_sam_u_invite_codes")
    .select("*")
    .eq("code", inviteCode)
    .eq("is_used", false)
    .single();

  if (inviteError || !inviteData) {
    throw new Error("Invalid or expired invite code");
  }

  if (inviteData.expires_at && new Date(inviteData.expires_at) < new Date()) {
    throw new Error("Invite code has expired");
  }

  return inviteData;
}

async function createUserAccount(email: string, password: string, fullName: string) {
  const supabase = await createClient();
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: fullName,
      },
    },
  });

  if (authError) {
    throw new Error(authError.message);
  }

  if (!authData.user) {
    throw new Error("Failed to create user account");
  }

  return authData.user;
}

export async function POST(request: NextRequest) {
  try {
    const body: RegisterRequest = await request.json();
    const { email, password, fullName, inviteCode, turnstileToken } = body;

    // Validate required fields
    if (!email || !password || !fullName || !inviteCode || !turnstileToken) {
      return NextResponse.json({ message: "All fields are required" }, { status: 400 });
    }

    // Validate Turnstile token
    if (!isValidTurnstileToken(turnstileToken)) {
      return NextResponse.json({ message: "Invalid verification token" }, { status: 400 });
    }

    // Verify Turnstile token with Cloudflare
    const isValidToken = await verifyTurnstileToken(
      turnstileToken,
      request.headers.get("x-forwarded-for") ?? undefined,
    );

    if (!isValidToken) {
      return NextResponse.json({ message: "Verification failed. Please try again." }, { status: 400 });
    }

    // Validate invite code
    const inviteData = await validateInviteCode(inviteCode);

    // Create user account
    const user = await createUserAccount(email, password, fullName);

    // Create user profile
    const adminSupabase = createAdminClient();
    const { error: profileError } = await adminSupabase.from("us_sam_u_profiles").insert({
      auth_user_id: user.id,
      email,
      full_name: fullName,
      role: "USER",
      is_active: true,
    });

    if (profileError) {
      // If profile creation fails, we should clean up the auth user
      await adminSupabase.auth.admin.deleteUser(user.id);
      return NextResponse.json({ message: "Failed to create user profile" }, { status: 500 });
    }

    // Mark invite code as used
    await adminSupabase
      .from("us_sam_u_invite_codes")
      .update({
        is_used: true,
        used_by: user.id,
        used_at: new Date().toISOString(),
      })
      .eq("id", inviteData.id);

    // Log registration
    await adminSupabase.from("us_sam_u_logs").insert({
      user_id: user.id,
      action: "USER_REGISTERED",
      details: {
        email,
        invite_code: inviteCode,
        ip_address: request.headers.get("x-forwarded-for") ?? "unknown",
        user_agent: request.headers.get("user-agent") ?? "unknown",
      },
    });

    return NextResponse.json({ message: "Registration successful" }, { status: 201 });
  } catch (error) {
    console.error("Registration error:", error);
    return NextResponse.json({ message: "Internal server error" }, { status: 500 });
  }
}
