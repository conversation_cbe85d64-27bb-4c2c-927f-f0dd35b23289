import { NextRequest, NextResponse } from "next/server";

import { createAdminClient } from "@/lib/supabase/admin";

interface ValidateInviteRequest {
  code: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: ValidateInviteRequest = await request.json();
    const { code } = body;

    if (!code) {
      return NextResponse.json({ message: "Invite code is required" }, { status: 400 });
    }

    const adminSupabase = createAdminClient();

    // Check if invite code exists and is not used
    const { data: inviteData, error: inviteError } = await adminSupabase
      .from("us_sam_u_invite_codes")
      .select("*")
      .eq("code", code)
      .eq("is_used", false)
      .single();

    if (inviteError || !inviteData) {
      return NextResponse.json({ message: "Invalid invite code" }, { status: 400 });
    }

    // Check if invite code is expired
    if (inviteData.expires_at && new Date(inviteData.expires_at) < new Date()) {
      return NextResponse.json({ message: "Invite code has expired" }, { status: 400 });
    }

    return NextResponse.json({ message: "Invite code is valid" }, { status: 200 });
  } catch (error) {
    console.error("Invite validation error:", error);
    return NextResponse.json({ message: "Internal server error" }, { status: 500 });
  }
}
