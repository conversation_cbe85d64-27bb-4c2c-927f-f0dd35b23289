import { NextRequest, NextResponse } from "next/server";

import { createAdminClient } from "@/lib/supabase/admin";
import { createClient } from "@/lib/supabase/server";
import { verifyTurnstileToken, isValidTurnstileToken } from "@/lib/turnstile";

interface ForgotPasswordRequest {
  email: string;
  turnstileToken?: string;
}

// Helper function to validate input
function validateInput(email: string, turnstileToken?: string): string | null {
  if (!email) {
    return "Email is required";
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return "Please enter a valid email address";
  }

  if (turnstileToken && !isValidTurnstileToken(turnstileToken)) {
    return "Invalid verification token";
  }

  return null;
}

// Helper function to send reset email
async function sendResetEmail(email: string, supabase: any): Promise<boolean> {
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password`,
  });

  if (error) {
    console.error("Password reset error:", error);
    return false;
  }

  return true;
}

export async function POST(request: NextRequest) {
  try {
    const body: ForgotPasswordRequest = await request.json();
    const { email, turnstileToken } = body;

    const validationError = validateInput(email, turnstileToken);
    if (validationError) {
      return NextResponse.json({ message: validationError }, { status: 400 });
    }

    // Validate Turnstile token if provided
    if (turnstileToken) {
      const isValidToken = await verifyTurnstileToken(
        turnstileToken,
        request.headers.get("x-forwarded-for") ?? undefined,
      );

      if (!isValidToken) {
        return NextResponse.json({ message: "Verification failed. Please try again." }, { status: 400 });
      }
    }

    const supabase = await createClient();
    const adminSupabase = createAdminClient();

    // Check if user exists in our system
    const { data: profile } = await adminSupabase
      .from("us_sam_u_profiles")
      .select("email, is_active")
      .eq("email", email)
      .single();

    if (!profile || !profile.is_active) {
      // For security, we don't reveal if the email exists or not
      return NextResponse.json(
        { message: "If an account with that email exists, we've sent a password reset link." },
        { status: 200 },
      );
    }

    const emailSent = await sendResetEmail(email, supabase);
    if (!emailSent) {
      return NextResponse.json({ message: "Failed to send reset email. Please try again." }, { status: 500 });
    }

    // Log the password reset request
    await adminSupabase.from("us_sam_u_logs").insert({
      user_id: null,
      action: "PASSWORD_RESET_REQUESTED",
      details: {
        email,
        ip_address: request.headers.get("x-forwarded-for") ?? "unknown",
        user_agent: request.headers.get("user-agent") ?? "unknown",
      },
    });

    return NextResponse.json(
      { message: "If an account with that email exists, we've sent a password reset link." },
      { status: 200 },
    );
  } catch (error) {
    console.error("Forgot password error:", error);
    return NextResponse.json({ message: "Internal server error" }, { status: 500 });
  }
}
