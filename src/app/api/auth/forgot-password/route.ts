import { NextRequest, NextResponse } from "next/server";

import { createAdminClient } from "@/lib/supabase/admin";
import { createClient } from "@/lib/supabase/server";
import { verifyTurnstileToken, isValidTurnstileToken } from "@/lib/turnstile";

interface ForgotPasswordRequest {
  email: string;
  turnstileToken?: string;
}

// Helper function to validate input
function validateInput(email: string, turnstileToken?: string): string | null {
  if (!email) {
    return "Email is required";
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return "Please enter a valid email address";
  }

  if (turnstileToken && !isValidTurnstileToken(turnstileToken)) {
    return "Invalid verification token";
  }

  return null;
}

// Helper function to send reset email
// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function sendResetEmail(email: string, supabase: any): Promise<boolean> {
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password`,
  });

  if (error) {
    console.error("Password reset error:", error);
    return false;
  }

  return true;
}

// Helper function to process password reset
// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function processPasswordReset(email: string, supabase: any, adminSupabase: any, request: NextRequest) {
  // Check if user exists in our system
  const { data: profile } = await adminSupabase
    .from("us_sam_u_profiles")
    .select("email, is_active")
    .eq("email", email)
    .single();

  if (!profile || !profile.is_active) {
    // For security, we don't reveal if the email exists or not
    return { success: true, message: "If an account with that email exists, we've sent a password reset link." };
  }

  const emailSent = await sendResetEmail(email, supabase);
  if (!emailSent) {
    return { success: false, message: "Failed to send reset email. Please try again." };
  }

  // Log the password reset request
  await adminSupabase.from("us_sam_u_logs").insert({
    user_id: null,
    action: "PASSWORD_RESET_REQUESTED",
    details: {
      email,
      ip_address: request.headers.get("x-forwarded-for") ?? "unknown",
      user_agent: request.headers.get("user-agent") ?? "unknown",
    },
  });

  return { success: true, message: "If an account with that email exists, we've sent a password reset link." };
}

export async function POST(request: NextRequest) {
  try {
    const body: ForgotPasswordRequest = await request.json();
    const { email, turnstileToken } = body;

    const validationError = validateInput(email, turnstileToken);
    if (validationError) {
      return NextResponse.json({ message: validationError }, { status: 400 });
    }

    // Validate Turnstile token if provided
    if (turnstileToken) {
      const isValidToken = await verifyTurnstileToken(
        turnstileToken,
        request.headers.get("x-forwarded-for") ?? undefined,
      );

      if (!isValidToken) {
        return NextResponse.json({ message: "Verification failed. Please try again." }, { status: 400 });
      }
    }

    const supabase = await createClient();
    const adminSupabase = createAdminClient();

    const result = await processPasswordReset(email, supabase, adminSupabase, request);

    return NextResponse.json(
      { message: result.message },
      { status: result.success ? 200 : 500 },
    );
  } catch (error) {
    console.error("Forgot password error:", error);
    return NextResponse.json({ message: "Internal server error" }, { status: 500 });
  }
}
