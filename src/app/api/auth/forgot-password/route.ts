import { NextRequest, NextResponse } from "next/server";

import { createAdminClient } from "@/lib/supabase/admin";
import { createClient } from "@/lib/supabase/server";

interface ForgotPasswordRequest {
  email: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: ForgotPasswordRequest = await request.json();
    const { email } = body;

    if (!email) {
      return NextResponse.json({ message: "Email is required" }, { status: 400 });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({ message: "Please enter a valid email address" }, { status: 400 });
    }

    const supabase = await createClient();

    // Check if user exists in our system
    const adminSupabase = createAdminClient();
    const { data: profile } = await adminSupabase
      .from("us_sam_u_profiles")
      .select("email, is_active")
      .eq("email", email)
      .single();

    if (!profile) {
      // For security, we don't reveal if the email exists or not
      return NextResponse.json(
        { message: "If an account with that email exists, we've sent a password reset link." },
        { status: 200 },
      );
    }

    if (!profile.is_active) {
      return NextResponse.json({ message: "Account is not active. Please contact support." }, { status: 400 });
    }

    // Send password reset email
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password`,
    });

    if (error) {
      console.error("Password reset error:", error);
      return NextResponse.json({ message: "Failed to send reset email. Please try again." }, { status: 500 });
    }

    // Log the password reset request
    await adminSupabase.from("us_sam_u_logs").insert({
      user_id: null, // We don't have the auth user ID at this point
      action: "PASSWORD_RESET_REQUESTED",
      details: {
        email,
        ip_address: request.headers.get("x-forwarded-for") ?? "unknown",
        user_agent: request.headers.get("user-agent") ?? "unknown",
      },
    });

    return NextResponse.json(
      { message: "If an account with that email exists, we've sent a password reset link." },
      { status: 200 },
    );
  } catch (error) {
    console.error("Forgot password error:", error);
    return NextResponse.json({ message: "Internal server error" }, { status: 500 });
  }
}
