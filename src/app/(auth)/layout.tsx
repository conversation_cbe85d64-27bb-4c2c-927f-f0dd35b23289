import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Authentication - HiddenGov',
  description: 'Sign in to your HiddenGov account',
}

interface AuthLayoutProps {
  children: React.ReactNode
}

export default function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="w-full max-w-md space-y-8 px-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground">HiddenGov</h1>
          <p className="text-sm text-muted-foreground mt-2">
            Government Contract Intelligence Platform
          </p>
        </div>
        {children}
      </div>
    </div>
  )
}
