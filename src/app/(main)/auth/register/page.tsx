import Link from "next/link";

import RegisterForm from "./_components/register-form";

export default function RegisterPage() {
  return (
    <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight">Create Account</h1>
        <p className="text-muted-foreground text-sm">
          Enter your details to create your HiddenGov account and start your 7-day free trial.
        </p>
      </div>
      <RegisterForm />
      <p className="text-muted-foreground px-8 text-center text-sm">
        Already have an account?{" "}
        <Link href="/auth/login" className="hover:text-primary underline underline-offset-4">
          Sign in
        </Link>
      </p>
      <p className="text-muted-foreground px-8 text-center text-sm">
        By clicking continue, you agree to our{" "}
        <Link href="/terms" className="hover:text-primary underline underline-offset-4">
          Terms of Service
        </Link>{" "}
        and{" "}
        <Link href="/privacy" className="hover:text-primary underline underline-offset-4">
          Privacy Policy
        </Link>
        .
      </p>
    </div>
  );
}
