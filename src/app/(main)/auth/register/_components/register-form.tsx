"use client";

import React, { useState } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderCircle } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import TurnstileWidget from "@/components/features/auth/turnstile-widget";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const FormSchema = z
  .object({
    fullName: z.string().min(2, { message: "Full name must be at least 2 characters long" }),
    email: z.string().email({ message: "Invalid email address" }),
    inviteCode: z.string().length(8, { message: "Invite code must be exactly 8 characters" }),
    password: z
      .string()
      .min(6, { message: "Password must be at least 6 characters long" })
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
        message: "Password must contain at least one uppercase letter, one lowercase letter, and one number",
      }),
    confirmPassword: z.string(),
    turnstileToken: z.string().optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export default function RegisterForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [turnstileToken, setTurnstileToken] = useState<string | null>(null);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      fullName: "",
      email: "",
      inviteCode: "",
      password: "",
      confirmPassword: "",
      turnstileToken: "",
    },
  });

  const onSubmit = async (data: z.infer<typeof FormSchema>) => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Check Turnstile verification if enabled
      if (process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY && !turnstileToken) {
        setError("Please complete the security verification.");
        setIsLoading(false);
        return;
      }
      // First validate invite code
      const inviteResponse = await fetch("/api/auth/validate-invite", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ code: data.inviteCode }),
      });

      if (!inviteResponse.ok) {
        const inviteError = await inviteResponse.json();
        setError(inviteError.message ?? "Invalid invite code");
        return;
      }

      // Then register user
      const registerResponse = await fetch("/api/auth/register", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          email: data.email,
          password: data.password,
          fullName: data.fullName,
          inviteCode: data.inviteCode,
          turnstileToken: turnstileToken ?? "development-mode",
        }),
      });

      if (!registerResponse.ok) {
        const registerError = await registerResponse.json();
        setError(registerError.message ?? "Registration failed");
        return;
      }

      setSuccess("Registration successful! Please check your email to verify your account.");
    } catch {
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {error && <div className="rounded-md border border-red-200 bg-red-50 p-3 text-sm text-red-600">{error}</div>}

        {success && (
          <div className="rounded-md border border-green-200 bg-green-50 p-3 text-sm text-green-600">{success}</div>
        )}

        <FormField
          control={form.control}
          name="fullName"
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel>Full Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter your full name" {...field} />
              </FormControl>
              <FormMessage>{fieldState.error?.message}</FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage>{fieldState.error?.message}</FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="inviteCode"
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel>Invite Code</FormLabel>
              <FormControl>
                <Input placeholder="Enter your invite code" maxLength={8} {...field} />
              </FormControl>
              <FormMessage>{fieldState.error?.message}</FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input type="password" placeholder="Create a password" {...field} />
              </FormControl>
              <FormMessage>{fieldState.error?.message}</FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel>Confirm Password</FormLabel>
              <FormControl>
                <Input type="password" placeholder="Confirm your password" {...field} />
              </FormControl>
              <FormMessage>{fieldState.error?.message}</FormMessage>
            </FormItem>
          )}
        />

        {process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Security Verification</label>
            <TurnstileWidget
              onVerify={(token) => {
                setTurnstileToken(token);
                form.setValue("turnstileToken", token);
                setError(null);
              }}
              onError={() => {
                setTurnstileToken(null);
                form.setValue("turnstileToken", "");
                setError("Security verification failed. Please try again.");
              }}
              onExpire={() => {
                setTurnstileToken(null);
                form.setValue("turnstileToken", "");
                setError("Security verification expired. Please try again.");
              }}
            />
          </div>
        )}

        <Button disabled={isLoading} className="w-full">
          {isLoading && <LoaderCircle className="mr-2 size-4 animate-spin" />}
          Create Account
        </Button>
      </form>
    </Form>
  );
}
