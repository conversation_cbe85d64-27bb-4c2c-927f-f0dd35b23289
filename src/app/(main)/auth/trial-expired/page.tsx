import Link from "next/link";

import { <PERSON>, CreditCard, Mail, ArrowRight } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function TrialExpiredPage() {
  return (
    <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[500px]">
      <Card>
        <CardHeader>
          <div className="flex flex-col items-center space-y-4">
            <div className="rounded-full bg-orange-100 p-3">
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
            <CardTitle className="text-center text-2xl">Trial Period Expired</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center">
            <p className="text-muted-foreground">
              Your 7-day free trial has ended. To continue accessing HiddenGov&apos;s powerful government contract
              intelligence platform, please choose a subscription plan.
            </p>
          </div>

          <div className="space-y-4">
            <div className="rounded-lg border bg-gradient-to-r from-blue-50 to-indigo-50 p-4">
              <h3 className="font-semibold text-blue-900">What you&apos;ll get with a subscription:</h3>
              <ul className="mt-2 space-y-1 text-sm text-blue-800">
                <li>• Unlimited access to government contract database</li>
                <li>• Advanced search and filtering capabilities</li>
                <li>• Real-time contract alerts and notifications</li>
                <li>• Contact information for procurement officers</li>
                <li>• Export and download functionality</li>
                <li>• Priority customer support</li>
              </ul>
            </div>

            <div className="grid gap-3 sm:grid-cols-2">
              <Button className="flex items-center justify-center space-x-2" size="lg">
                <CreditCard className="h-4 w-4" />
                <span>Choose Plan</span>
                <ArrowRight className="h-4 w-4" />
              </Button>

              <Button variant="outline" className="flex items-center justify-center space-x-2" size="lg">
                <Mail className="h-4 w-4" />
                <span>Contact Sales</span>
              </Button>
            </div>
          </div>

          <div className="border-t pt-4">
            <div className="space-y-2 text-center">
              <p className="text-muted-foreground text-sm">
                Need more time to evaluate? Contact our sales team for an extended trial.
              </p>
              <div className="flex justify-center space-x-4 text-sm">
                <Link href="mailto:<EMAIL>" className="text-primary hover:underline">
                  <EMAIL>
                </Link>
                <span className="text-muted-foreground">•</span>
                <Link href="tel:******-0123" className="text-primary hover:underline">
                  (*************
                </Link>
              </div>
            </div>
          </div>

          <div className="border-t pt-4">
            <div className="flex justify-center space-x-4">
              <Link href="/auth/login" className="text-muted-foreground hover:text-primary text-sm">
                Back to Sign In
              </Link>
              <span className="text-muted-foreground">•</span>
              <Link href="/dashboard" className="text-muted-foreground hover:text-primary text-sm">
                Dashboard
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
