import { Suspense } from "react";

import Link from "next/link";

import ResetPasswordForm from "./_components/reset-password-form";

export default function ResetPasswordPage() {
  return (
    <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight">Set New Password</h1>
        <p className="text-muted-foreground text-sm">Enter your new password below to complete the reset process.</p>
      </div>
      <Suspense fallback={<div>Loading...</div>}>
        <ResetPasswordForm />
      </Suspense>
      <p className="text-muted-foreground px-8 text-center text-sm">
        Remember your password?{" "}
        <Link href="/auth/login" className="hover:text-primary underline underline-offset-4">
          Sign in
        </Link>
      </p>
    </div>
  );
}
