"use client";

import { useEffect, useState, Suspense } from "react";

import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";

import { CheckCircle, XCircle, LoaderCircle, Mail } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

function VerifyEmailContent() {
  const [status, setStatus] = useState<"loading" | "success" | "error" | "already-verified">("loading");
  const [message, setMessage] = useState("");
  const [userEmail, setUserEmail] = useState("");

  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        const accessToken = searchParams.get("access_token");
        const refreshToken = searchParams.get("refresh_token");
        const type = searchParams.get("type");

        if (type !== "signup" || !accessToken || !refreshToken) {
          setStatus("error");
          setMessage("Invalid verification link. Please check your email for the correct link.");
          return;
        }

        const response = await fetch("/api/auth/verify-email", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            accessToken,
            refreshToken,
          }),
        });

        const data = await response.json();

        if (response.ok) {
          if (data.alreadyVerified) {
            setStatus("already-verified");
            setMessage("Your email is already verified. You can sign in to your account.");
          } else {
            setStatus("success");
            setMessage("Your email has been successfully verified! You can now sign in to your account.");
          }
          setUserEmail(data.email ?? "");
        } else {
          setStatus("error");
          setMessage(data.message ?? "Email verification failed. Please try again.");
        }
      } catch (error) {
        console.error("Email verification error:", error);
        setStatus("error");
        setMessage("An unexpected error occurred. Please try again.");
      }
    };

    verifyEmail();
  }, [searchParams]);

  const handleResendVerification = async () => {
    if (!userEmail) return;

    try {
      const response = await fetch("/api/auth/resend-verification", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: userEmail }),
      });

      if (response.ok) {
        setMessage("Verification email sent! Please check your inbox.");
      } else {
        setMessage("Failed to resend verification email. Please try again.");
      }
    } catch (error) {
      console.error("Resend verification error:", error);
      setMessage("An error occurred while resending the verification email.");
    }
  };

  const getIcon = () => {
    switch (status) {
      case "loading":
        return <LoaderCircle className="h-12 w-12 animate-spin text-blue-500" />;
      case "success":
      case "already-verified":
        return <CheckCircle className="h-12 w-12 text-green-500" />;
      case "error":
        return <XCircle className="h-12 w-12 text-red-500" />;
      default:
        return <Mail className="h-12 w-12 text-gray-500" />;
    }
  };

  const getTitle = () => {
    switch (status) {
      case "loading":
        return "Verifying your email...";
      case "success":
        return "Email verified successfully!";
      case "already-verified":
        return "Email already verified";
      case "error":
        return "Verification failed";
      default:
        return "Email verification";
    }
  };

  return (
    <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
      <Card>
        <CardHeader>
          <div className="flex flex-col items-center space-y-4">
            {getIcon()}
            <CardTitle className="text-center">{getTitle()}</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground text-center text-sm">{message}</p>

          {status === "success" || status === "already-verified" ? (
            <div className="space-y-3">
              <Button onClick={() => router.push("/auth/login")} className="w-full">
                Continue to Sign In
              </Button>
              {userEmail && <p className="text-muted-foreground text-center text-xs">Verified email: {userEmail}</p>}
            </div>
          ) : status === "error" ? (
            <div className="space-y-3">
              {userEmail && (
                <Button onClick={handleResendVerification} variant="outline" className="w-full">
                  Resend Verification Email
                </Button>
              )}
              <Button onClick={() => router.push("/auth/register")} variant="outline" className="w-full">
                Back to Registration
              </Button>
            </div>
          ) : null}

          <div className="text-center">
            <Link href="/auth/login" className="text-primary text-sm hover:underline">
              Back to Sign In
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function VerifyEmailPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <VerifyEmailContent />
    </Suspense>
  );
}
