"use client";

import React, { useState, useEffect } from "react";

import Link from "next/link";

import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderCircle } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import TurnstileWidget from "@/components/features/auth/turnstile-widget";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const FormSchema = z.object({
  email: z.string().email({ message: "Invalid email address" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters long" }),
  rememberMe: z.boolean().default(false),
  turnstileToken: z.string().optional(),
});

export default function LoginForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [turnstileToken, setTurnstileToken] = useState<string | null>(null);
  const [showTurnstile, setShowTurnstile] = useState(false);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
      turnstileToken: "",
    },
  });

  // Load remembered email on component mount
  useEffect(() => {
    const rememberMe = localStorage.getItem("hgov_remember_me");
    const savedEmail = localStorage.getItem("hgov_user_email");

    if (rememberMe === "true" && savedEmail) {
      form.setValue("email", savedEmail);
      form.setValue("rememberMe", true);
    }
  }, [form]);

  const onSubmit = async (data: z.infer<typeof FormSchema>) => {
    setIsLoading(true);
    setError(null);

    try {
      // Check Turnstile verification if enabled
      if (process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY && !turnstileToken) {
        setShowTurnstile(true);
        setError("Please complete the security verification.");
        setIsLoading(false);
        return;
      }

      // Import Supabase client dynamically to avoid SSR issues
      const { createClient } = await import("@/lib/supabase/client");
      const supabase = createClient();

      const { data: authData, error } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      });

      if (error) {
        console.error("Login error:", error.message);

        // Handle specific error cases
        if (error.message.includes("Invalid login credentials")) {
          setError("Invalid email or password. Please check your credentials and try again.");
        } else if (error.message.includes("Email not confirmed")) {
          setError("Please verify your email address before signing in. Check your inbox for a verification link.");
        } else if (error.message.includes("Too many requests")) {
          setError("Too many login attempts. Please wait a few minutes before trying again.");
        } else {
          setError(error.message);
        }
        return;
      }

      // Handle "Remember Me" functionality
      if (data.rememberMe) {
        // Store session preference in localStorage
        localStorage.setItem("hgov_remember_me", "true");
        localStorage.setItem("hgov_user_email", data.email);
      } else {
        localStorage.removeItem("hgov_remember_me");
        localStorage.removeItem("hgov_user_email");
      }

      // Log successful login
      await fetch("/api/auth/log-activity", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "USER_LOGIN",
          details: { remember_me: data.rememberMe },
        }),
      }).catch((err) => console.error("Failed to log activity:", err));

      // Redirect will be handled by middleware
      window.location.href = "/dashboard";
    } catch (error) {
      console.error("Unexpected error:", error);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {error && (
          <div className="rounded-md border border-red-200 bg-red-50 p-3">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <FormField
          control={form.control}
          name="email"
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage>{fieldState.error?.message}</FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field, fieldState }) => (
            <FormItem>
              <div className="flex items-center justify-between">
                <FormLabel>Password</FormLabel>
                <Link href="/auth/forgot-password" className="text-primary text-sm hover:underline">
                  Forgot password?
                </Link>
              </div>
              <FormControl>
                <Input type="password" placeholder="Password" {...field} />
              </FormControl>
              <FormMessage>{fieldState.error?.message}</FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="rememberMe"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-y-0 space-x-3">
              <FormControl>
                <Checkbox checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel className="text-sm font-normal">Remember me for 30 days</FormLabel>
              </div>
            </FormItem>
          )}
        />

        {(showTurnstile || process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY) && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Security Verification</label>
            <TurnstileWidget
              onVerify={(token) => {
                setTurnstileToken(token);
                form.setValue("turnstileToken", token);
                setError(null);
              }}
              onError={() => {
                setTurnstileToken(null);
                form.setValue("turnstileToken", "");
                setError("Security verification failed. Please try again.");
              }}
              onExpire={() => {
                setTurnstileToken(null);
                form.setValue("turnstileToken", "");
                setError("Security verification expired. Please try again.");
              }}
            />
          </div>
        )}

        <Button disabled={isLoading} className="w-full">
          {isLoading && <LoaderCircle className="mr-2 size-4 animate-spin" />}
          Sign In
        </Button>
      </form>
    </Form>
  );
}
