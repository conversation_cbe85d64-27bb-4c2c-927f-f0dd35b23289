export type Json = string | number | boolean | null | { [key: string]: J<PERSON> | undefined } | Json[];

export interface Database {
  public: {
    Tables: {
      us_sam_u_profiles: {
        Row: {
          id: string;
          auth_user_id: string;
          email: string;
          full_name: string | null;
          avatar_url: string | null;
          is_active: boolean;
          role: "USER" | "ADMIN" | "SUPER";
          subscription_start: string | null;
          subscription_end: string | null;
          invited_by: string | null;
          invite_code_used: string | null;
          trial_start: string;
          trial_end: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          auth_user_id: string;
          email: string;
          full_name?: string | null;
          avatar_url?: string | null;
          is_active?: boolean;
          role?: "USER" | "ADMIN" | "SUPER";
          subscription_start?: string | null;
          subscription_end?: string | null;
          invited_by?: string | null;
          invite_code_used?: string | null;
          trial_start?: string;
          trial_end?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          auth_user_id?: string;
          email?: string;
          full_name?: string | null;
          avatar_url?: string | null;
          is_active?: boolean;
          role?: "USER" | "ADMIN" | "SUPER";
          subscription_start?: string | null;
          subscription_end?: string | null;
          invited_by?: string | null;
          invite_code_used?: string | null;
          trial_start?: string;
          trial_end?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      us_sam_u_invite_codes: {
        Row: {
          id: string;
          code: string;
          is_used: boolean;
          used_by: string | null;
          created_by: string | null;
          max_uses: number;
          current_uses: number;
          expires_at: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
          used_at: string | null;
        };
        Insert: {
          id?: string;
          code: string;
          is_used?: boolean;
          used_by?: string | null;
          created_by?: string | null;
          max_uses?: number;
          current_uses?: number;
          expires_at?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
          used_at?: string | null;
        };
        Update: {
          id?: string;
          code?: string;
          is_used?: boolean;
          used_by?: string | null;
          created_by?: string | null;
          max_uses?: number;
          current_uses?: number;
          expires_at?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
          used_at?: string | null;
        };
      };
      us_sam_u_logs: {
        Row: {
          id: string;
          user_id: string;
          action: string;
          details: Json | null;
          ip_address: string | null;
          user_agent: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          action: string;
          details?: Json | null;
          ip_address?: string | null;
          user_agent?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          action?: string;
          details?: Json | null;
          ip_address?: string | null;
          user_agent?: string | null;
          created_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      user_role: "USER" | "ADMIN" | "SUPER";
    };
  };
}
