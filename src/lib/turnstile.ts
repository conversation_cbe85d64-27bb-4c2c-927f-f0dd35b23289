/**
 * Cloudflare Turnstile verification utilities
 */

interface TurnstileResponse {
  success: boolean;
  "error-codes"?: string[];
  challenge_ts?: string;
  hostname?: string;
}

/**
 * Verify a Turnstile token with Cloudflare
 */
export async function verifyTurnstileToken(token: string, remoteip?: string): Promise<boolean> {
  // Skip verification in development mode if no secret key is configured
  if (!process.env.TURNSTILE_SECRET_KEY) {
    console.warn("Turnstile secret key not configured, skipping verification in development mode");
    return token === "development-mode";
  }

  // Skip verification for demo tokens
  if (token === "demo-token" || token === "development-mode") {
    return true;
  }

  try {
    const formData = new FormData();
    formData.append("secret", process.env.TURNSTILE_SECRET_KEY);
    formData.append("response", token);

    if (remoteip) {
      formData.append("remoteip", remoteip);
    }

    const response = await fetch("https://challenges.cloudflare.com/turnstile/v0/siteverify", {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      console.error("Turnstile verification request failed:", response.status, response.statusText);
      return false;
    }

    const data: TurnstileResponse = await response.json();

    if (!data.success) {
      console.error("Turnstile verification failed:", data["error-codes"]);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Turnstile verification error:", error);
    return false;
  }
}

/**
 * Get user-friendly error message for Turnstile error codes
 */
export function getTurnstileErrorMessage(errorCodes?: string[]): string {
  if (!errorCodes || errorCodes.length === 0) {
    return "Verification failed. Please try again.";
  }

  const errorCode = errorCodes[0];

  switch (errorCode) {
    case "missing-input-secret":
      return "Server configuration error. Please contact support.";
    case "invalid-input-secret":
      return "Server configuration error. Please contact support.";
    case "missing-input-response":
      return "Please complete the verification challenge.";
    case "invalid-input-response":
      return "Verification failed. Please try again.";
    case "bad-request":
      return "Invalid request. Please refresh the page and try again.";
    case "timeout-or-duplicate":
      return "Verification expired or already used. Please try again.";
    default:
      return "Verification failed. Please try again.";
  }
}

/**
 * Check if Turnstile is enabled in the current environment
 */
export function isTurnstileEnabled(): boolean {
  return !!(process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY && process.env.TURNSTILE_SECRET_KEY);
}

/**
 * Validate Turnstile token format
 */
export function isValidTurnstileToken(token: string): boolean {
  if (!token) return false;

  // Allow development mode tokens
  if (token === "demo-token" || token === "development-mode") {
    return true;
  }

  // Turnstile tokens are typically long base64-like strings
  return token.length > 20 && /^[A-Za-z0-9._-]+$/.test(token);
}
