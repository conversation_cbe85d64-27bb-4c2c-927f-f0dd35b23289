"use client";

import { useEffect, useRef, useCallback } from "react";

interface TurnstileWidgetProps {
  onVerify: (token: string) => void;
  onError?: () => void;
  onExpire?: () => void;
  onLoad?: () => void;
  theme?: "light" | "dark" | "auto";
  size?: "normal" | "compact";
  className?: string;
}

interface TurnstileOptions {
  sitekey: string;
  callback: (token: string) => void;
  "error-callback"?: () => void;
  "expired-callback"?: () => void;
  "before-interactive-callback"?: () => void;
  "after-interactive-callback"?: () => void;
  theme?: "light" | "dark" | "auto";
  size?: "normal" | "compact";
  tabindex?: number;
}

declare global {
  interface Window {
    turnstile: {
      render: (element: HTMLElement | string, options: TurnstileOptions) => string;
      reset: (widgetId: string) => void;
      remove: (widgetId: string) => void;
      getResponse: (widgetId: string) => string;
    };
    onloadTurnstileCallback?: () => void;
  }
}

export default function TurnstileWidget({
  onVerify,
  onError,
  onExpire,
  onLoad,
  theme = "auto",
  size = "normal",
  className = "",
}: TurnstileWidgetProps) {
  const widgetRef = useRef<HTMLDivElement>(null);
  const widgetIdRef = useRef<string | null>(null);
  const isLoadedRef = useRef(false);

  const renderWidget = useCallback(() => {
    if (!widgetRef.current || !window.turnstile) {
      return;
    }

    if (isLoadedRef.current) {
      return;
    }

    const siteKey = process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY;
    if (!siteKey) {
      console.error("Turnstile site key is not configured");
      onError?.();
      return;
    }

    try {
      widgetIdRef.current = window.turnstile.render(widgetRef.current, {
        sitekey: siteKey,
        callback: onVerify,
        "error-callback": onError,
        "expired-callback": onExpire,
        "before-interactive-callback": () => {
          onLoad?.();
        },
        theme,
        size,
        tabindex: 0,
      });
      isLoadedRef.current = true;
    } catch (error) {
      console.error("Failed to render Turnstile widget:", error);
      onError?.();
    }
  }, [onVerify, onError, onExpire, onLoad, theme, size]);

  const loadTurnstileScript = useCallback(() => {
    if (document.querySelector('script[src*="turnstile"]')) {
      return;
    }

    const script = document.createElement("script");
    script.src = "https://challenges.cloudflare.com/turnstile/v0/api.js?onload=onloadTurnstileCallback";
    script.async = true;
    script.defer = true;

    window.onloadTurnstileCallback = () => {
      renderWidget();
    };

    script.onerror = () => {
      console.error("Failed to load Turnstile script");
      onError?.();
    };

    document.head.appendChild(script);
  }, [renderWidget, onError]);

  useEffect(() => {
    if (window.turnstile) {
      renderWidget();
    } else {
      loadTurnstileScript();
    }

    return () => {
      if (widgetIdRef.current) {
        try {
          if (window.turnstile) {
            window.turnstile.remove(widgetIdRef.current);
          }
        } catch (error) {
          console.error("Failed to remove Turnstile widget:", error);
        }
      }
      isLoadedRef.current = false;
    };
  }, [renderWidget, loadTurnstileScript]);

  // Reset widget method
  const reset = useCallback(() => {
    if (widgetIdRef.current) {
      try {
        if (window.turnstile) {
          window.turnstile.reset(widgetIdRef.current);
        }
      } catch (error) {
        console.error("Failed to reset Turnstile widget:", error);
      }
    }
  }, []);

  // Expose reset method via ref
  useEffect(() => {
    if (widgetRef.current) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (widgetRef.current as any).reset = reset;
    }
  }, [reset]);

  return (
    <div className={`turnstile-widget ${className}`}>
      <div ref={widgetRef} />
      {!process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY && (
        <div className="rounded border border-yellow-200 bg-yellow-50 p-3 text-sm text-yellow-800">
          <p className="font-medium">Development Mode</p>
          <p>Turnstile verification is disabled. Configure NEXT_PUBLIC_TURNSTILE_SITE_KEY to enable.</p>
        </div>
      )}
    </div>
  );
}
