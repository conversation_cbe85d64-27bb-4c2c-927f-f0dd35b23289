#!/usr/bin/env node

/**
 * HiddenGov 项目设置测试脚本
 * 用于验证项目的基本配置和依赖是否正确
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 HiddenGov 项目设置检查开始...\n');

// 检查必要的文件
const requiredFiles = [
  'package.json',
  '.env.example',
  'src/lib/supabase/client.ts',
  'src/lib/supabase/server.ts',
  'src/lib/supabase/admin.ts',
  'src/types/database.ts',
  'src/app/(main)/auth/login/_components/login-form.tsx',
  'src/app/(main)/auth/register/page.tsx',
  'src/app/api/auth/register/route.ts',
  'src/middleware.ts',
  'docs/implementation-summary.md'
];

console.log('📁 检查必要文件...');
let missingFiles = [];

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file}`);
    missingFiles.push(file);
  }
});

if (missingFiles.length > 0) {
  console.log(`\n⚠️  缺失 ${missingFiles.length} 个必要文件`);
  process.exit(1);
} else {
  console.log('\n✅ 所有必要文件都存在');
}

// 检查 package.json 中的依赖
console.log('\n📦 检查关键依赖...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredDeps = [
  '@supabase/supabase-js',
  '@supabase/ssr',
  'next',
  'react',
  'typescript',
  '@hookform/resolvers',
  'react-hook-form',
  'zod'
];

let missingDeps = [];

requiredDeps.forEach(dep => {
  if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
    console.log(`✅ ${dep}`);
  } else {
    console.log(`❌ ${dep}`);
    missingDeps.push(dep);
  }
});

if (missingDeps.length > 0) {
  console.log(`\n⚠️  缺失 ${missingDeps.length} 个必要依赖`);
  console.log('请运行: npm install');
  process.exit(1);
} else {
  console.log('\n✅ 所有必要依赖都已安装');
}

// 检查环境变量模板
console.log('\n🔧 检查环境变量配置...');
const envExample = fs.readFileSync('.env.example', 'utf8');
const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY'
];

let missingEnvVars = [];

requiredEnvVars.forEach(envVar => {
  if (envExample.includes(envVar)) {
    console.log(`✅ ${envVar}`);
  } else {
    console.log(`❌ ${envVar}`);
    missingEnvVars.push(envVar);
  }
});

if (missingEnvVars.length > 0) {
  console.log(`\n⚠️  .env.example 缺失 ${missingEnvVars.length} 个必要环境变量`);
} else {
  console.log('\n✅ 环境变量模板配置正确');
}

// 检查 .env.local 是否存在
if (fs.existsSync('.env.local')) {
  console.log('✅ .env.local 文件存在');

  const envLocal = fs.readFileSync('.env.local', 'utf8');
  let configuredVars = 0;

  requiredEnvVars.forEach(envVar => {
    if (envLocal.includes(envVar) && !envLocal.includes(`${envVar}=your_`)) {
      configuredVars++;
    }
  });

  if (configuredVars === requiredEnvVars.length) {
    console.log('✅ 环境变量已配置');
  } else {
    console.log(`⚠️  ${requiredEnvVars.length - configuredVars} 个环境变量需要配置实际值`);
  }
} else {
  console.log('⚠️  .env.local 文件不存在，请复制 .env.example 并配置实际值');
}

// 检查 TypeScript 配置
console.log('\n🔍 检查 TypeScript 配置...');
if (fs.existsSync('tsconfig.json')) {
  console.log('✅ tsconfig.json 存在');
} else {
  console.log('❌ tsconfig.json 不存在');
}

// 检查 Tailwind 配置
console.log('\n🎨 检查 Tailwind CSS 配置...');
if (fs.existsSync('postcss.config.mjs')) {
  console.log('✅ postcss.config.mjs 存在 (Tailwind CSS v4)');
} else {
  console.log('❌ postcss.config.mjs 不存在');
}

console.log('\n🎉 项目设置检查完成！');

// 提供下一步指导
console.log('\n📋 下一步操作:');
console.log('1. 确保已配置 .env.local 文件中的 Supabase 连接信息');
console.log('2. 在 Supabase 中执行 docs/sql/ 目录下的数据库脚本');
console.log('3. 运行 npm run dev 启动开发服务器');
console.log('4. 访问 http://localhost:3000 查看应用');

console.log('\n📚 参考文档:');
console.log('- docs/implementation-summary.md - 实施总结');
console.log('- docs/hiddengov-tdd-v2.md - 技术设计文档');
console.log('- README.md - 完整的安装和使用指南');

console.log('\n✨ HiddenGov 项目已准备就绪！');
